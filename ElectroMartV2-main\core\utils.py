import numpy as np
import tensorflow as tf
from tensorflow.keras.preprocessing import image
from tensorflow.keras.applications.efficientnet import EfficientNetB0, preprocess_input
from .category_mapping import CATEGORY_MAPPING
import os
from django.conf import settings

# Đường dẫn đến model phân loại
CLASSIFICATION_MODEL_PATH = os.path.join(settings.BASE_DIR, 'model', 'model.keras')

# Kiểm tra và load model phân loại
if not os.path.exists(CLASSIFICATION_MODEL_PATH):
    raise FileNotFoundError(f"Classification model not found at {CLASSIFICATION_MODEL_PATH}")

try:
    classification_model = tf.keras.models.load_model(CLASSIFICATION_MODEL_PATH)
    print("✅ Classification model loaded successfully!")
except Exception as e:
    print(f"❌ Error loading classification model: {e}")
    classification_model = None

# Tạo model trích xuất đặc trưng
feature_extractor = EfficientNetB0(
    weights='imagenet',
    include_top=False,
    pooling='avg'
)

# <PERSON>h sách lớp từ mô hình
MODEL_CLASSES = [
    "T-Shirt", "Longsleeve", "Pants", "Shoes", "Shirt", 
    "Dress", "Outwear", "Shorts", "Not sure", "Hat", 
    "Skirt", "Polo", "Undershirt", "Blazer", "Hoodie", 
    "Body", "Other", "Top", "Blouse", "Skip"
]

def classify_product(img_path):
    """Phân loại sản phẩm từ ảnh"""
    if classification_model is None:
        return "Model not loaded", "Lỗi hệ thống"
    
    try:
        # Load và tiền xử lý ảnh
        img = image.load_img(img_path, target_size=(224, 224))
        img_array = image.img_to_array(img)
        img_array = np.expand_dims(img_array, axis=0)
        img_array = preprocess_input(img_array)
        
        # Dự đoán
        predictions = classification_model.predict(img_array)
        predicted_class_idx = np.argmax(predictions[0])
        predicted_class = MODEL_CLASSES[predicted_class_idx]
        
        # Ánh xạ sang danh mục hệ thống
        category_name = CATEGORY_MAPPING.get(predicted_class, "Không xác định")
        
        return predicted_class, category_name
    except Exception as e:
        print(f"Error in classification: {e}")
        return "Classification error", "Lỗi xử lý"

def extract_features(img_path):
    """Trích xuất đặc trưng từ ảnh sử dụng EfficientNetB0"""
    try:
        img = image.load_img(img_path, target_size=(224, 224))
        img_array = image.img_to_array(img)
        img_array = np.expand_dims(img_array, axis=0)
        img_array = preprocess_input(img_array)
        
        features = feature_extractor.predict(img_array)
        return features.flatten()
    except Exception as e:
        print(f"Error extracting features: {e}")
        return None

def calculate_similarity(features1, features2):
    """Tính toán độ tương đồng cosine giữa hai vector"""
    if features1 is None or features2 is None:
        return 0
        
    dot_product = np.dot(features1, features2)
    norm1 = np.linalg.norm(features1)
    norm2 = np.linalg.norm(features2)
    
    if norm1 == 0 or norm2 == 0:
        return 0
        
    return dot_product / (norm1 * norm2)