from django.urls import path
from . import views

urlpatterns = [
    path('', views.home, name='home'),
    path('register/', views.register_view, name='register'),
    path('login/', views.login_view, name='login'),
    path('logout/', views.logout_view, name='logout'),
    path('profile/', views.profile_view, name='profile'),
    path('change-password/', views.change_password_view, name='change_password'),
    path('about/', views.about_view, name='about'),
    path('contact/', views.contact, name='contact'),
    path('products/', views.products, name='products'),
    path('search-by-image/', views.upload_image_ai, name='upload_image_ai'),
    # Admin URLs
    path('admin/dashboard/', views.admin_dashboard, name='admin_dashboard'),
    
    # Contact management
    path('admin/contacts/', views.admin_contacts, name='admin_contacts'),
    path('admin/contacts/<int:contact_id>/', views.admin_contact_detail, name='admin_contact_detail'),
    path('admin/contacts/<int:contact_id>/respond/', views.admin_contact_respond, name='admin_contact_respond'),
    path('admin/contacts/<int:contact_id>/delete/', views.admin_contact_delete, name='admin_contact_delete'),
    
    # Category management
    path('admin/categories/', views.admin_categories, name='admin_categories'),
    path('admin/categories/create/', views.admin_category_create, name='admin_category_create'),
    path('admin/categories/<int:category_id>/', views.admin_category_detail, name='admin_category_detail'),
    path('admin/categories/<int:category_id>/update/', views.admin_category_update, name='admin_category_update'),
    path('admin/categories/<int:category_id>/delete/', views.admin_category_delete, name='admin_category_delete'),
    
    # Product management
    path('admin/products/', views.admin_products, name='admin_products'),
    path('admin/products/create/', views.admin_product_create, name='admin_product_create'),
    path('admin/products/<int:product_id>/edit/', views.admin_product_edit, name='admin_product_edit'),
    path('admin/products/<int:product_id>/delete/', views.admin_product_delete, name='admin_product_delete'),
    path('product/<int:product_id>/', views.product_detail, name='product_detail'),
    path('cart/', views.cart_view, name='cart'),
    path('add-to-cart/<int:product_id>/', views.add_to_cart, name='add_to_cart'),
    path('remove-from-cart/<int:product_id>/', views.remove_from_cart, name='remove_from_cart'),
    path('update-cart/<int:product_id>/', views.update_cart, name='update_cart'),
    path('checkout/', views.checkout, name='checkout'),
    path('order-success/<int:order_id>/', views.order_success, name='order_success'),
    path('category/<slug:category_slug>/', views.category_detail, name='category'),
    path('my-orders/', views.my_orders, name='my_orders'),
    path('order-detail/<int:order_id>/', views.order_detail, name='order_detail'),
    path('cancel-order/<int:order_id>/', views.cancel_order, name='cancel_order'),
    
    # User Review Management
    path('my-reviews/', views.my_reviews, name='my_reviews'),
    path('edit-review/<int:review_id>/', views.edit_review, name='edit_review'),
    path('delete-review/<int:review_id>/', views.delete_review, name='delete_review'),
    path('create-zalopay-order/', views.create_zalopay_order, name='create_zalopay_order'),
    path('zalopay-callback/', views.zalopay_callback, name='zalopay_callback'),
    path('admin/promotions/', views.PromotionListView.as_view(), name='admin_promotions'),
    path('admin/promotions/create/', views.admin_promotion_create, name='admin_promotion_create'),
    path('admin/promotions/<int:promotion_id>/update/', views.admin_promotion_update, name='admin_promotion_update'),
    path('admin/promotions/<int:promotion_id>/delete/', views.admin_promotion_delete, name='admin_promotion_delete'),
    path('admin/promotions/<int:promotion_id>/detail/', views.admin_promotion_detail, name='admin_promotion_detail'),
    path('apply-promotion/', views.apply_promotion, name='apply_promotion'),
    path('remove-promotion/', views.remove_promotion, name='remove_promotion'),

    # Admin Order Management
    path('admin/orders/', views.admin_orders, name='admin_orders'),
    path('admin/orders/<int:order_id>/', views.admin_order_detail, name='admin_order_detail'),
    path('admin/orders/<int:order_id>/update-status/', views.admin_update_order_status, name='admin_update_order_status'),
    path('admin/orders/<int:order_id>/update-payment/', views.admin_update_payment_status, name='admin_update_payment_status'),
    path('admin/orders/export/', views.admin_export_orders, name='admin_export_orders'),
    path('admin/products/<int:product_id>/detail/', views.admin_product_detail_json, name='admin_product_detail_json'),

    # Admin News Management
    path('admin/news/', views.admin_news, name='admin_news'),
    path('admin/news/create/', views.admin_create_news, name='admin_create_news'),
    path('admin/news/<int:news_id>/edit/', views.admin_edit_news, name='admin_edit_news'),
    path('admin/news/<int:news_id>/delete/', views.admin_delete_news, name='admin_delete_news'),
    
    # Public News
    path('news/', views.news_list, name='news'),
    path('news/<slug:slug>/', views.news_detail, name='news_detail'),
    path('news/increase-views/<int:news_id>/', views.news_increase_views, name='news_increase_views'),

    # Inventory Management
    path('admin/inventory/', views.admin_inventory, name='admin_inventory'),
    path('admin/inventory/create/', views.admin_inventory_create, name='admin_inventory_create'),
    path('admin/inventory/<int:transaction_id>/', views.admin_inventory_detail, name='admin_inventory_detail'),
    path('admin/inventory/<int:transaction_id>/delete/', views.admin_inventory_delete, name='admin_inventory_delete'),
    path('admin/inventory/export/', views.admin_export_inventory, name='admin_export_inventory'),

    # User Management
    path('admin/users/', views.admin_users, name='admin_users'),
    path('admin/users/<int:user_id>/detail/', views.admin_user_detail, name='admin_user_detail'),
    path('admin/users/<int:user_id>/toggle-status/', views.admin_user_toggle_status, name='admin_user_toggle_status'),

    # Chat URLs
    path('chat/', views.chat_room, name='chat_room'),
    path('chat/<int:room_id>/', views.chat_room, name='chat_room_detail'),
    path('chat/create/', views.create_chat_room, name='create_chat_room'),
    path('chat/unread-count/', views.chat_unread_count, name='chat_unread_count'),
    path('admin/chats/', views.admin_chat_list, name='admin_chat_list'),
    path('admin/chats/<int:room_id>/', views.admin_chat_room, name='admin_chat_room'),
    
    # Chatbot API
    path('api/chatbot/', views.chatbot_api, name='chatbot_api'),
    path('api/test-chatbot/', views.test_chatbot_api, name='test_chatbot_api'),
    path('chatbot/', views.chatbot_view, name='chatbot'),
] 