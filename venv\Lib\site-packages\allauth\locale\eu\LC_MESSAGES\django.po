# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
msgid ""
msgstr ""
"Project-Id-Version: django-allauth\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-01-23 12:50-0600\n"
"PO-Revision-Date: 2018-08-29 08:16+0200\n"
"Last-Translator: <PERSON>ek<PERSON> <<EMAIL>>\n"
"Language-Team: Basque <<EMAIL>>\n"
"Language: eu\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 2.1.1\n"

#: account/adapter.py:50
msgid "Username can not be used. Please use other username."
msgstr ""
"Erabiltzaile izen hau ezin da erabili. Aukeratu beste erabiltzaile izen bat."

#: account/adapter.py:56
msgid "Too many failed login attempts. Try again later."
msgstr "Huts egite gehiegi saioa hasterakoan. Saiatu berriro beranduago."

#: account/adapter.py:58
msgid "A user is already registered with this email address."
msgstr ""
"Erabiltzaile batek kontu bat sortu du iada helbide elektroniko honekin."

#: account/adapter.py:59
msgid "Please type your current password."
msgstr "Mesedez idatzi zure oraingo pasahitza."

#: account/adapter.py:60
#, fuzzy
#| msgid "Current Password"
msgid "Incorrect password."
msgstr "Oraingo pasahitza"

#: account/adapter.py:61
#, python-brace-format
msgid "Password must be a minimum of {0} characters."
msgstr "Pasahitzak gutxienez {0} karaktere izan behar ditu."

#: account/adapter.py:62
msgid "The email address is not assigned to any user account"
msgstr "Helbide elektroniko hau ez dago kontu bati lotuta"

#: account/adapter.py:739
#, fuzzy
#| msgid "Forgot Password?"
msgid "Use your password"
msgstr "Pasahitza ahaztu duzu?"

#: account/adapter.py:749
msgid "Use your authenticator app"
msgstr ""

#: account/admin.py:23
#, fuzzy
#| msgid "Your primary email address must be verified."
msgid "Mark selected email addresses as verified"
msgstr "Zure email nagusiak egiaztatuta egon behar du."

#: account/apps.py:11
msgid "Accounts"
msgstr "Kontuak"

#: account/forms.py:60 account/forms.py:449
msgid "You must type the same password each time."
msgstr "Pasahitz berdina idatzi behar duzu aldi bakoitzean."

#: account/forms.py:92 account/forms.py:412 account/forms.py:554
#: account/forms.py:683
msgid "Password"
msgstr "Pasahitza"

#: account/forms.py:93
msgid "Remember Me"
msgstr "Gogora nazazue"

#: account/forms.py:97
msgid "This account is currently inactive."
msgstr "Kontu hau ez dago aktiboa orain."

#: account/forms.py:99
msgid "The email address and/or password you specified are not correct."
msgstr "Sartutako helbide elektronikoa eta/edo pasahitza ez dira zuzenak."

#: account/forms.py:102
msgid "The username and/or password you specified are not correct."
msgstr "Sartutako erabiltzailea eta/edo pasahitza ez dira zuzenak."

#: account/forms.py:112 account/forms.py:287 account/forms.py:477
#: account/forms.py:574
msgid "Email address"
msgstr "Helbide elektronikoa"

#: account/forms.py:116 account/forms.py:325 account/forms.py:474
#: account/forms.py:569
msgid "Email"
msgstr "Emaila"

#: account/forms.py:119 account/forms.py:122 account/forms.py:277
#: account/forms.py:280
msgid "Username"
msgstr "Erabiltzailea"

#: account/forms.py:132
msgid "Username or email"
msgstr "Erabiltzailea edo emaila"

#: account/forms.py:135
msgctxt "field label"
msgid "Login"
msgstr "Logina"

#: account/forms.py:146
#, fuzzy
#| msgid "Forgot Password?"
msgid "Forgot your password?"
msgstr "Pasahitza ahaztu duzu?"

#: account/forms.py:316
msgid "Email (again)"
msgstr "Emaila (berriro)"

#: account/forms.py:320
msgid "Email address confirmation"
msgstr "Helbide elektronikoaren egiaztapena"

#: account/forms.py:328
msgid "Email (optional)"
msgstr "Emaila (hautazkoa)"

#: account/forms.py:383
msgid "You must type the same email each time."
msgstr "Email berdina idatzi behar duzu aldi bakoitzean."

#: account/forms.py:418 account/forms.py:557
msgid "Password (again)"
msgstr "Pasahitza (berriro)"

#: account/forms.py:489
msgid "This email address is already associated with this account."
msgstr "Helbide elektroniko hau dagoeneko kontu honi lotuta dago."

#: account/forms.py:491
#, python-format
msgid "You cannot add more than %d email addresses."
msgstr "Ezin dituzu %d email helbide baino gehiago erabili."

#: account/forms.py:529
msgid "Current Password"
msgstr "Oraingo pasahitza"

#: account/forms.py:532 account/forms.py:632
msgid "New Password"
msgstr "Pasahitz berria"

#: account/forms.py:535 account/forms.py:633
msgid "New Password (again)"
msgstr "Pasahitz berria (berriro)"

#: account/forms.py:653
msgid "The password reset token was invalid."
msgstr "Pasahitza berrezartzeko \"token\"-a baliogabea da."

#: account/models.py:21
msgid "user"
msgstr "erabiltzailea"

#: account/models.py:26 account/models.py:34 account/models.py:138
msgid "email address"
msgstr "helbide elektronikoa"

#: account/models.py:28
msgid "verified"
msgstr "egiaztatuta"

#: account/models.py:29
msgid "primary"
msgstr "nagusia"

#: account/models.py:35
msgid "email addresses"
msgstr "helbide elektronikoak"

#: account/models.py:141
msgid "created"
msgstr "sortuta"

#: account/models.py:142
msgid "sent"
msgstr "bidalita"

#: account/models.py:143 socialaccount/models.py:63
msgid "key"
msgstr "giltza"

#: account/models.py:148
msgid "email confirmation"
msgstr "email egiaztapena"

#: account/models.py:149
msgid "email confirmations"
msgstr "email egiaztapenak"

#: mfa/adapter.py:20
msgid ""
"You cannot activate two-factor authentication until you have verified your "
"email address."
msgstr ""

#: mfa/adapter.py:23
msgid ""
"You cannot add an email address to an account protected by two-factor "
"authentication."
msgstr ""

#: mfa/adapter.py:25
msgid "Incorrect code."
msgstr ""

#: mfa/adapter.py:27
msgid "You cannot deactivate two-factor authentication."
msgstr ""

#: mfa/apps.py:9
msgid "MFA"
msgstr ""

#: mfa/forms.py:15 mfa/forms.py:17 mfa/forms.py:52
msgid "Code"
msgstr ""

#: mfa/forms.py:50
msgid "Authenticator code"
msgstr ""

#: mfa/models.py:19
msgid "Recovery codes"
msgstr ""

#: mfa/models.py:20
msgid "TOTP Authenticator"
msgstr ""

#: socialaccount/adapter.py:31
#, fuzzy, python-format
#| msgid ""
#| "An account already exists with this e-mail address. Please sign in to "
#| "that account first, then connect your %s account."
msgid ""
"An account already exists with this email address. Please sign in to that "
"account first, then connect your %s account."
msgstr ""
"Kontu bat sortu da iada helbide elektroniko honekin. Mesedez hasi saio berri "
"bat kontu honekin eta gero zure %s kontua honi lotu."

#: socialaccount/adapter.py:148
msgid "Your account has no password set up."
msgstr "Zure kontuak ez du pasahitzik zehaztuta."

#: socialaccount/adapter.py:155
msgid "Your account has no verified email address."
msgstr "Zure kontuak ez du egiaztatutako emailik."

#: socialaccount/apps.py:9
msgid "Social Accounts"
msgstr "Sare sozial kontuak"

#: socialaccount/models.py:37 socialaccount/models.py:91
msgid "provider"
msgstr "zerbitzua"

#: socialaccount/models.py:46
#, fuzzy
#| msgid "provider"
msgid "provider ID"
msgstr "zerbitzua"

#: socialaccount/models.py:50
msgid "name"
msgstr "izena"

#: socialaccount/models.py:52
msgid "client id"
msgstr "client id"

#: socialaccount/models.py:54
msgid "App ID, or consumer key"
msgstr "Aplikazioaren ID-a, edo \"consumer key\"-a"

#: socialaccount/models.py:57
msgid "secret key"
msgstr "\"secret key\"-a"

#: socialaccount/models.py:60
msgid "API secret, client secret, or consumer secret"
msgstr "\"API secret\"-a, \"client secret\"-a edo \"consumer secret\"-a"

#: socialaccount/models.py:63
msgid "Key"
msgstr "Giltza"

#: socialaccount/models.py:75
msgid "social application"
msgstr "aplikazio soziala"

#: socialaccount/models.py:76
msgid "social applications"
msgstr "aplikazio sozialak"

#: socialaccount/models.py:111
msgid "uid"
msgstr "uid"

#: socialaccount/models.py:113
msgid "last login"
msgstr "azken logina"

#: socialaccount/models.py:114
msgid "date joined"
msgstr "erregistro eguna"

#: socialaccount/models.py:115
msgid "extra data"
msgstr "datu gehigarriak"

#: socialaccount/models.py:119
msgid "social account"
msgstr "sare sozial kontua"

#: socialaccount/models.py:120
msgid "social accounts"
msgstr "sare sozial kontuak"

#: socialaccount/models.py:154
msgid "token"
msgstr "\"token\"-a"

#: socialaccount/models.py:155
msgid "\"oauth_token\" (OAuth1) or access token (OAuth2)"
msgstr "\"oauth_token\"-a (OAuth1) edo \"access token\"-a (OAuth2)"

#: socialaccount/models.py:159
msgid "token secret"
msgstr "\"token secret\"-a"

#: socialaccount/models.py:160
msgid "\"oauth_token_secret\" (OAuth1) or refresh token (OAuth2)"
msgstr "\"oauth_token_secret\"-a (OAuth1) edo \"refresh token\"-a (OAuth2)"

#: socialaccount/models.py:163
msgid "expires at"
msgstr "iraungitze data"

#: socialaccount/models.py:168
msgid "social application token"
msgstr "aplikazio sozial \"token\"-a"

#: socialaccount/models.py:169
msgid "social application tokens"
msgstr "aplikazio sozial \"token\"-ak"

#: socialaccount/providers/douban/views.py:37
msgid "Invalid profile data"
msgstr "Profil datu baliogabeak"

#: socialaccount/providers/oauth/client.py:85
#, fuzzy, python-format
#| msgid "Invalid response while obtaining request token from \"%s\"."
msgid ""
"Invalid response while obtaining request token from \"%s\". Response was: %s."
msgstr "Erantzun baliogabea \"%s\"-tik \"request token\"-a eskuratzean."

#: socialaccount/providers/oauth/client.py:119
#: socialaccount/providers/pocket/client.py:86
#, python-format
msgid "Invalid response while obtaining access token from \"%s\"."
msgstr "Erantzun baliogabea \"%s\"-tik \"access token\"-a eskuratzean."

#: socialaccount/providers/oauth/client.py:140
#, python-format
msgid "No request token saved for \"%s\"."
msgstr "Ez dago \"request token\"-ik gordeta \"%s\"-entzat."

#: socialaccount/providers/oauth/client.py:191
#, python-format
msgid "No access token saved for \"%s\"."
msgstr "Ez dago \"access token\"-ik gordeta \"%s\"-entzat."

#: socialaccount/providers/oauth/client.py:212
#, python-format
msgid "No access to private resources at \"%s\"."
msgstr "Ez duzu baliabide pribatuetara sarbiderik: \"%s\"."

#: socialaccount/providers/pocket/client.py:41
#, python-format
msgid "Invalid response while obtaining request token from \"%s\"."
msgstr "Erantzun baliogabea \"%s\"-tik \"request token\"-a eskuratzean."

#: templates/account/account_inactive.html:5
#: templates/account/account_inactive.html:9
msgid "Account Inactive"
msgstr "Kontu ez aktiboa"

#: templates/account/account_inactive.html:11
msgid "This account is inactive."
msgstr "Kontu hau ez dago aktiboa."

#: templates/account/base_reauthenticate.html:5
#: templates/account/base_reauthenticate.html:9
#, fuzzy
#| msgid "Confirm Email Address"
msgid "Confirm Access"
msgstr "Helbide elektronikoa egiaztatu"

#: templates/account/base_reauthenticate.html:11
msgid "Please reauthenticate to safeguard your account."
msgstr ""

#: templates/account/base_reauthenticate.html:17
msgid "Alternative options"
msgstr ""

#: templates/account/email.html:4 templates/account/email.html:8
msgid "Email Addresses"
msgstr "Helbide elektronikoak"

#: templates/account/email.html:11
msgid "The following email addresses are associated with your account:"
msgstr "Helbide elektroniko hauek zure kontuari lotuta daude:"

#: templates/account/email.html:23
msgid "Verified"
msgstr "Egiaztatuta"

#: templates/account/email.html:27
msgid "Unverified"
msgstr "Egiaztatu gabe"

#: templates/account/email.html:32
msgid "Primary"
msgstr "Nagusia"

#: templates/account/email.html:42
msgid "Make Primary"
msgstr "Nagusia egin"

#: templates/account/email.html:45 templates/account/email_change.html:37
msgid "Re-send Verification"
msgstr "Egiaztapen emaila berbidali"

#: templates/account/email.html:48 templates/socialaccount/connections.html:40
msgid "Remove"
msgstr "Ezabatu"

#: templates/account/email.html:57
msgid "Add Email Address"
msgstr "Helbide elektronikoa gehitu"

#: templates/account/email.html:70
msgid "Add Email"
msgstr "Emaila gehitu"

#: templates/account/email.html:79
msgid "Do you really want to remove the selected email address?"
msgstr "Ziur al zaude aukeratutako helbide elektronikoa ezabatu nahi duzula?"

#: templates/account/email/account_already_exists_message.txt:4
#, python-format
msgid ""
"You are receiving this email because you or someone else tried to signup for "
"an\n"
"account using email address:\n"
"\n"
"%(email)s\n"
"\n"
"However, an account using that email address already exists.  In case you "
"have\n"
"forgotten about this, please use the password forgotten procedure to "
"recover\n"
"your account:\n"
"\n"
"%(password_reset_url)s"
msgstr ""

#: templates/account/email/account_already_exists_subject.txt:3
msgid "Account Already Exists"
msgstr ""

#: templates/account/email/base_message.txt:1
#, python-format
msgid "Hello from %(site_name)s!"
msgstr "Kaixo %(site_name)s webgunetik!"

#: templates/account/email/base_message.txt:5
#, python-format
msgid ""
"Thank you for using %(site_name)s!\n"
"%(site_domain)s"
msgstr ""
"Mila esker %(site_name)s webgunea erabiltzeagatik!\n"
"%(site_domain)s"

#: templates/account/email/base_notification.txt:5
msgid ""
"You are receiving this mail because the following change was made to your "
"account:"
msgstr ""

#: templates/account/email/base_notification.txt:10
#, python-format
msgid ""
"If you do not recognize this change then please take proper security "
"precautions immediately. The change to your account originates from:\n"
"\n"
"- IP address: %(ip)s\n"
"- Browser: %(user_agent)s\n"
"- Date: %(timestamp)s"
msgstr ""

#: templates/account/email/email_changed_message.txt:4
#, python-format
msgid "Your email has been changed from %(from_email)s to %(to_email)s."
msgstr ""

#: templates/account/email/email_changed_subject.txt:3
#, fuzzy
#| msgid "Email address"
msgid "Email Changed"
msgstr "Helbide elektronikoa"

#: templates/account/email/email_confirm_message.txt:4
#, fuzzy
#| msgid "You have confirmed %(email)s."
msgid "Your email has been confirmed."
msgstr "%(email)s emaila egiaztatu duzu."

#: templates/account/email/email_confirm_subject.txt:3
#, fuzzy
#| msgid "email confirmation"
msgid "Email Confirmation"
msgstr "email egiaztapena"

#: templates/account/email/email_confirmation_message.txt:5
#, fuzzy, python-format
#| msgid ""
#| "You're receiving this e-mail because user %(user_display)s has given your "
#| "e-mail address to register an account on %(site_domain)s.\n"
#| "\n"
#| "To confirm this is correct, go to %(activate_url)s"
msgid ""
"You're receiving this email because user %(user_display)s has given your "
"email address to register an account on %(site_domain)s.\n"
"\n"
"To confirm this is correct, go to %(activate_url)s"
msgstr ""
"Email hau jaso duzu %(user_display)s erabiltzaileak zure helbide "
"elektronikoa bere kontuarekin lotu nahi duelako %(site_domain)s orrialdean.\n"
"\n"
"Hau zuzena dela baieztatzeko, egin klik hemen: %(activate_url)s"

#: templates/account/email/email_confirmation_subject.txt:3
msgid "Please Confirm Your Email Address"
msgstr "Mesedez egiaztatu zure helbide elektronikoa"

#: templates/account/email/email_deleted_message.txt:4
#, python-format
msgid "Email address %(deleted_email)s has been removed from your account."
msgstr ""

#: templates/account/email/email_deleted_subject.txt:3
#, fuzzy
#| msgid "Remove"
msgid "Email Removed"
msgstr "Ezabatu"

#: templates/account/email/password_changed_message.txt:4
#, fuzzy
#| msgid "Your password is now changed."
msgid "Your password has been changed."
msgstr "Zure pasahitza aldatuta dago orain."

#: templates/account/email/password_changed_subject.txt:3
#, fuzzy
#| msgid "Password (again)"
msgid "Password Changed"
msgstr "Pasahitza (berriro)"

#: templates/account/email/password_reset_key_message.txt:4
#, fuzzy
#| msgid ""
#| "You're receiving this e-mail because you or someone else has requested a "
#| "password for your user account.\n"
#| "It can be safely ignored if you did not request a password reset. Click "
#| "the link below to reset your password."
msgid ""
"You're receiving this email because you or someone else has requested a "
"password reset for your user account.\n"
"It can be safely ignored if you did not request a password reset. Click the "
"link below to reset your password."
msgstr ""
"Email hau jaso duzu zuk edo beste norbaitek pasahitza berrezartzeko eskaera "
"egin duelako zure kontuarentzat.\n"
"Eskaera zuk egin ez baduzu mezu hau alde batera utzi dezakezu. Edo egin klik "
"ondorengo estekan zure pasahitza berrezartzeko."

#: templates/account/email/password_reset_key_message.txt:9
#, python-format
msgid "In case you forgot, your username is %(username)s."
msgstr "Ahaztu baduzu, zure erabiltzaile izena %(username)s da."

#: templates/account/email/password_reset_key_subject.txt:3
#: templates/account/email/unknown_account_subject.txt:3
msgid "Password Reset Email"
msgstr "Pasahitza berrezartzeko emaila"

#: templates/account/email/password_reset_message.txt:4
#, fuzzy
#| msgid "Your password is now changed."
msgid "Your password has been reset."
msgstr "Zure pasahitza aldatuta dago orain."

#: templates/account/email/password_reset_subject.txt:3
#: templates/account/password_reset.html:4
#: templates/account/password_reset.html:8
#: templates/account/password_reset_done.html:6
#: templates/account/password_reset_done.html:10
msgid "Password Reset"
msgstr "Pasahitza berrezarri"

#: templates/account/email/password_set_message.txt:4
#, fuzzy
#| msgid "Your password is now changed."
msgid "Your password has been set."
msgstr "Zure pasahitza aldatuta dago orain."

#: templates/account/email/password_set_subject.txt:3
#, fuzzy
#| msgid "Password Reset"
msgid "Password Set"
msgstr "Pasahitza berrezarri"

#: templates/account/email/unknown_account_message.txt:4
#, fuzzy, python-format
#| msgid ""
#| "You're receiving this e-mail because you or someone else has requested a "
#| "password for your user account.\n"
#| "It can be safely ignored if you did not request a password reset. Click "
#| "the link below to reset your password."
msgid ""
"You are receiving this email because you or someone else has requested a\n"
"password for your user account. However, we do not have any record of a "
"user\n"
"with email %(email)s in our database.\n"
"\n"
"This mail can be safely ignored if you did not request a password reset.\n"
"\n"
"If it was you, you can sign up for an account using the link below."
msgstr ""
"Email hau jaso duzu zuk edo beste norbaitek pasahitza berrezartzeko eskaera "
"egin duelako zure kontuarentzat.\n"
"Eskaera zuk egin ez baduzu mezu hau alde batera utzi dezakezu. Edo egin klik "
"ondorengo estekan zure pasahitza berrezartzeko."

#: templates/account/email_change.html:5 templates/account/email_change.html:9
#, fuzzy
#| msgid "Email Addresses"
msgid "Email Address"
msgstr "Helbide elektronikoak"

#: templates/account/email_change.html:21
#: templates/account/email_change.html:29
#, fuzzy
#| msgid "Current Password"
msgid "Current email"
msgstr "Oraingo pasahitza"

#: templates/account/email_change.html:31
msgid "Changing to"
msgstr ""

#: templates/account/email_change.html:35
#, fuzzy
#| msgid "Your primary email address must be verified."
msgid "Your email address is still pending verification."
msgstr "Zure email nagusiak egiaztatuta egon behar du."

#: templates/account/email_change.html:41
msgid "Cancel Change"
msgstr ""

#: templates/account/email_change.html:49
#, fuzzy
#| msgid "Email"
msgid "Change to"
msgstr "Emaila"

#: templates/account/email_change.html:55
#: templates/allauth/layouts/base.html:29
#, fuzzy
#| msgid "Email"
msgid "Change Email"
msgstr "Emaila"

#: templates/account/email_confirm.html:6
#: templates/account/email_confirm.html:10
msgid "Confirm Email Address"
msgstr "Helbide elektronikoa egiaztatu"

#: templates/account/email_confirm.html:16
#, fuzzy, python-format
#| msgid ""
#| "Please confirm that <a href=\"mailto:%(email)s\">%(email)s</a> is an e-"
#| "mail address for user %(user_display)s."
msgid ""
"Please confirm that <a href=\"mailto:%(email)s\">%(email)s</a> is an email "
"address for user %(user_display)s."
msgstr ""
"Mesedez egiaztatu <a href=\"mailto:%(email)s\">%(email)s</a> "
"%(user_display)s erabiltzailearen helbide elektroniko bat dela."

#: templates/account/email_confirm.html:23
#: templates/account/reauthenticate.html:20
#: templates/mfa/reauthenticate.html:20
msgid "Confirm"
msgstr "Egiaztatu"

#: templates/account/email_confirm.html:29
#: templates/account/messages/email_confirmation_failed.txt:2
#, fuzzy, python-format
#| msgid "The social account is already connected to a different account."
msgid ""
"Unable to confirm %(email)s because it is already confirmed by a different "
"account."
msgstr "Sare sozial kontua dagoeneko beste kontu bati lotuta dago."

#: templates/account/email_confirm.html:35
#, fuzzy, python-format
#| msgid ""
#| "This e-mail confirmation link expired or is invalid. Please <a "
#| "href=\"%(email_url)s\">issue a new e-mail confirmation request</a>."
msgid ""
"This email confirmation link expired or is invalid. Please <a "
"href=\"%(email_url)s\">issue a new email confirmation request</a>."
msgstr ""
"Egiaztapen esteka hau iraungirik dago edo baliogabea da. Mesedez eskatu <a "
"href=\"%(email_url)s\">egiaztapen email berri bat</a>."

#: templates/account/login.html:5 templates/account/login.html:9
#: templates/account/login.html:29 templates/allauth/layouts/base.html:36
#: templates/mfa/authenticate.html:5 templates/mfa/authenticate.html:23
#: templates/openid/login.html:5 templates/openid/login.html:9
#: templates/openid/login.html:20 templates/socialaccount/login.html:5
msgid "Sign In"
msgstr "Saioa hasi"

#: templates/account/login.html:12
#, fuzzy, python-format
#| msgid ""
#| "If you have not created an account yet, then please\n"
#| "<a href=\"%(signup_url)s\">sign up</a> first."
msgid ""
"If you have not created an account yet, then please\n"
"    <a href=\"%(signup_url)s\">sign up</a> first."
msgstr ""
"Oraindik kontu bat sortu ez baduzu, mesedez\n"
"<a href=\"%(signup_url)s\">sortu kontu bat</a> lehenik."

#: templates/account/logout.html:4 templates/account/logout.html:8
#: templates/account/logout.html:23 templates/allauth/layouts/base.html:32
#: templates/usersessions/usersession_list.html:64
msgid "Sign Out"
msgstr "Saioa amaitu"

#: templates/account/logout.html:10
msgid "Are you sure you want to sign out?"
msgstr "Ziur al zaude saioa amaitu nahi duzula?"

#: templates/account/messages/cannot_delete_primary_email.txt:2
#, python-format
msgid "You cannot remove your primary email address (%(email)s)."
msgstr "Ezin duzu zure helbide elektroniko nagusia ezabatu (%(email)s)."

#: templates/account/messages/email_confirmation_sent.txt:2
#, python-format
msgid "Confirmation email sent to %(email)s."
msgstr "Egiaztapen emaila bidali da %(email)s helbidera."

#: templates/account/messages/email_confirmed.txt:2
#, python-format
msgid "You have confirmed %(email)s."
msgstr "%(email)s emaila egiaztatu duzu."

#: templates/account/messages/email_deleted.txt:2
#, python-format
msgid "Removed email address %(email)s."
msgstr "%(email)s helbide elektronikoa ezabatu da."

#: templates/account/messages/logged_in.txt:4
#, python-format
msgid "Successfully signed in as %(name)s."
msgstr "%(name)s bezala hasi duzu saioa."

#: templates/account/messages/logged_out.txt:2
msgid "You have signed out."
msgstr "Saioa amaitu duzu."

#: templates/account/messages/password_changed.txt:2
msgid "Password successfully changed."
msgstr "Pasahitza behar bezala aldatu da."

#: templates/account/messages/password_set.txt:2
msgid "Password successfully set."
msgstr "Pasahitza behar bezala zehaztu da."

#: templates/account/messages/primary_email_set.txt:2
msgid "Primary email address set."
msgstr "Helbide elektroniko nagusia zehaztu da."

#: templates/account/messages/unverified_primary_email.txt:2
msgid "Your primary email address must be verified."
msgstr "Zure email nagusiak egiaztatuta egon behar du."

#: templates/account/password_change.html:4
#: templates/account/password_change.html:8
#: templates/account/password_change.html:19
#: templates/account/password_reset_from_key.html:5
#: templates/account/password_reset_from_key.html:12
#: templates/account/password_reset_from_key.html:29
#: templates/account/password_reset_from_key_done.html:5
#: templates/account/password_reset_from_key_done.html:9
msgid "Change Password"
msgstr "Pasahitza aldatu"

#: templates/account/password_change.html:21
msgid "Forgot Password?"
msgstr "Pasahitza ahaztu duzu?"

#: templates/account/password_reset.html:14
#, fuzzy
#| msgid ""
#| "Forgotten your password? Enter your e-mail address below, and we'll send "
#| "you an e-mail allowing you to reset it."
msgid ""
"Forgotten your password? Enter your email address below, and we'll send you "
"an email allowing you to reset it."
msgstr ""
"Zure pasahitza ahaztu al duzu? Idatzi zure helbide elektronikoa hemen eta "
"pasahitza berrezartzeko email bat bidaliko dizugu."

#: templates/account/password_reset.html:25
msgid "Reset My Password"
msgstr "Nire pasahitza berrezarri"

#: templates/account/password_reset.html:29
msgid "Please contact us if you have any trouble resetting your password."
msgstr ""
"Mesedez jarri gurekin kontaktuan zure pasahitza berrezartzeko arazorik "
"baduzu."

#: templates/account/password_reset_done.html:16
#, fuzzy
#| msgid ""
#| "We have sent an e-mail to you for\n"
#| "verification. Please click on the link inside this e-mail. Please\n"
#| "contact us if you do not receive it within a few minutes."
msgid ""
"We have sent you an email. If you have not received it please check your "
"spam folder. Otherwise contact us if you do not receive it in a few minutes."
msgstr ""
"Email bat bidali dizugu zure helbidea egiaztatzeko.\n"
"Mesedez egin klik bertan aurkituko duzun estekan,\n"
"edo jarri gurekin kontaktuan hurrengo minutuetan\n"
"emailik jasotzen ez baduzu."

#: templates/account/password_reset_from_key.html:10
msgid "Bad Token"
msgstr "Token baliogabea"

#: templates/account/password_reset_from_key.html:18
#, python-format
msgid ""
"The password reset link was invalid, possibly because it has already been "
"used.  Please request a <a href=\"%(passwd_reset_url)s\">new password reset</"
"a>."
msgstr ""
"Pasahitza berrezartzeko esteka baliogabea da, beharbada lehendik ere erabili "
"delako. Mesedez eskatu <a href=\"%(passwd_reset_url)s\">pasahitza "
"berrezartzeko email berri bat</a>."

#: templates/account/password_reset_from_key_done.html:11
msgid "Your password is now changed."
msgstr "Zure pasahitza aldatuta dago orain."

#: templates/account/password_set.html:5 templates/account/password_set.html:9
#: templates/account/password_set.html:20
msgid "Set Password"
msgstr "Pasahitza zehaztu"

#: templates/account/reauthenticate.html:5
#, fuzzy
#| msgid "Forgot Password?"
msgid "Enter your password:"
msgstr "Pasahitza ahaztu duzu?"

#: templates/account/signup.html:4 templates/socialaccount/signup.html:5
msgid "Signup"
msgstr "Kontua sortu"

#: templates/account/signup.html:8 templates/account/signup.html:27
#: templates/allauth/layouts/base.html:39 templates/socialaccount/signup.html:9
#: templates/socialaccount/signup.html:29
msgid "Sign Up"
msgstr "Kontua sortu"

#: templates/account/signup.html:11
#, python-format
msgid ""
"Already have an account? Then please <a href=\"%(login_url)s\">sign in</a>."
msgstr ""
"Lehendik kontu bat sortua duzu? <a href=\"%(login_url)s\">Saioa hasi</a> "
"orduan."

#: templates/account/signup_closed.html:5
#: templates/account/signup_closed.html:9
msgid "Sign Up Closed"
msgstr "Ezin da konturik sortu iada"

#: templates/account/signup_closed.html:11
msgid "We are sorry, but the sign up is currently closed."
msgstr "Sentitzen dugu baina ezin da kontu berririk sortu."

#: templates/account/snippets/already_logged_in.html:7
msgid "Note"
msgstr "Oharra"

#: templates/account/snippets/already_logged_in.html:7
#, fuzzy, python-format
#| msgid "you are already logged in as %(user_display)s."
msgid "You are already logged in as %(user_display)s."
msgstr "lehendik saioa hasita duzu %(user_display)s bezala."

#: templates/account/snippets/warn_no_email.html:3
msgid "Warning:"
msgstr "Adi:"

#: templates/account/snippets/warn_no_email.html:3
#, fuzzy
#| msgid ""
#| "You currently do not have any e-mail address set up. You should really "
#| "add an e-mail address so you can receive notifications, reset your "
#| "password, etc."
msgid ""
"You currently do not have any email address set up. You should really add an "
"email address so you can receive notifications, reset your password, etc."
msgstr ""
"Oraingoz ez duzu helbide elektronikorik zehaztu. Helbide elektroniko bat "
"gehitu beharko zenuke notifikazioak jaso ahal izateko, pasahitza "
"berrezartzeko, etab."

#: templates/account/verification_sent.html:5
#: templates/account/verification_sent.html:9
#: templates/account/verified_email_required.html:5
#: templates/account/verified_email_required.html:9
msgid "Verify Your Email Address"
msgstr "Zure helbide elektronikoa egiaztatu"

#: templates/account/verification_sent.html:12
#, fuzzy
#| msgid ""
#| "We have sent an e-mail to you for verification. Follow the link provided "
#| "to finalize the signup process. Please contact us if you do not receive "
#| "it within a few minutes."
msgid ""
"We have sent an email to you for verification. Follow the link provided to "
"finalize the signup process. If you do not see the verification email in "
"your main inbox, check your spam folder. Please contact us if you do not "
"receive the verification email within a few minutes."
msgstr ""
"Email bat bidali dizugu zure helbidea egiaztatzeko. Mesedez egin klik bertan "
"aurkituko duzun estekan kontua sortzeko prozesua amaitzeko, edo jarri "
"gurekin kontaktuan hurrengo minutuetan emailik jasotzen ez baduzu."

#: templates/account/verified_email_required.html:13
#, fuzzy
#| msgid ""
#| "This part of the site requires us to verify that\n"
#| "you are who you claim to be. For this purpose, we require that you\n"
#| "verify ownership of your e-mail address. "
msgid ""
"This part of the site requires us to verify that\n"
"you are who you claim to be. For this purpose, we require that you\n"
"verify ownership of your email address. "
msgstr ""
"Webguneko atal honek zuk diozuna zarela egiaztatzea\n"
"eskatzen digu. Honetarako zure helbide elektronikoa\n"
"egiaztatzea beharrezkoa da. "

#: templates/account/verified_email_required.html:18
#, fuzzy
#| msgid ""
#| "We have sent an e-mail to you for\n"
#| "verification. Please click on the link inside this e-mail. Please\n"
#| "contact us if you do not receive it within a few minutes."
msgid ""
"We have sent an email to you for\n"
"verification. Please click on the link inside that email. If you do not see "
"the verification email in your main inbox, check your spam folder. "
"Otherwise\n"
"contact us if you do not receive it within a few minutes."
msgstr ""
"Email bat bidali dizugu zure helbidea egiaztatzeko.\n"
"Mesedez egin klik bertan aurkituko duzun estekan,\n"
"edo jarri gurekin kontaktuan hurrengo minutuetan\n"
"emailik jasotzen ez baduzu."

#: templates/account/verified_email_required.html:23
#, fuzzy, python-format
#| msgid ""
#| "<strong>Note:</strong> you can still <a href=\"%(email_url)s\">change "
#| "your e-mail address</a>."
msgid ""
"<strong>Note:</strong> you can still <a href=\"%(email_url)s\">change your "
"email address</a>."
msgstr ""
"<strong>Oharra:</strong> oraindik <a href=\"%(email_url)s\">zure helbide "
"elektronikoa aldatu</a> dezakezu."

#: templates/allauth/layouts/base.html:18
msgid "Messages:"
msgstr ""

#: templates/allauth/layouts/base.html:25
msgid "Menu:"
msgstr ""

#: templates/mfa/authenticate.html:9 templates/mfa/index.html:5
#: templates/mfa/index.html:9
msgid "Two-Factor Authentication"
msgstr ""

#: templates/mfa/authenticate.html:12
msgid ""
"Your account is protected by two-factor authentication. Please enter an "
"authenticator code:"
msgstr ""

#: templates/mfa/authenticate.html:27
msgid "Cancel"
msgstr ""

#: templates/mfa/email/recovery_codes_generated_message.txt:4
msgid ""
"A new set of Two-Factor Authentication recovery codes has been generated."
msgstr ""

#: templates/mfa/email/recovery_codes_generated_subject.txt:3
msgid "New Recovery Codes Generated"
msgstr ""

#: templates/mfa/email/totp_activated_message.txt:4
#: templates/mfa/messages/totp_activated.txt:2
msgid "Authenticator app activated."
msgstr ""

#: templates/mfa/email/totp_activated_subject.txt:3
#, fuzzy
#| msgid "token secret"
msgid "Authenticator App Activated"
msgstr "\"token secret\"-a"

#: templates/mfa/email/totp_deactivated_message.txt:4
#: templates/mfa/messages/totp_deactivated.txt:2
msgid "Authenticator app deactivated."
msgstr ""

#: templates/mfa/email/totp_deactivated_subject.txt:3
#, fuzzy
#| msgid "token secret"
msgid "Authenticator App Deactivated"
msgstr "\"token secret\"-a"

#: templates/mfa/index.html:14 templates/mfa/totp/base.html:4
msgid "Authenticator App"
msgstr ""

#: templates/mfa/index.html:18
msgid "Authentication using an authenticator app is active."
msgstr ""

#: templates/mfa/index.html:20
msgid "An authenticator app is not active."
msgstr ""

#: templates/mfa/index.html:28 templates/mfa/totp/deactivate_form.html:24
msgid "Deactivate"
msgstr ""

#: templates/mfa/index.html:32 templates/mfa/totp/activate_form.html:32
msgid "Activate"
msgstr ""

#: templates/mfa/index.html:42 templates/mfa/recovery_codes/base.html:4
#: templates/mfa/recovery_codes/generate.html:6
#: templates/mfa/recovery_codes/index.html:6
msgid "Recovery Codes"
msgstr ""

#: templates/mfa/index.html:47 templates/mfa/recovery_codes/index.html:9
#, python-format
msgid ""
"There is %(unused_count)s out of %(total_count)s recovery codes available."
msgid_plural ""
"There are %(unused_count)s out of %(total_count)s recovery codes available."
msgstr[0] ""
msgstr[1] ""

#: templates/mfa/index.html:50
msgid "No recovery codes set up."
msgstr ""

#: templates/mfa/index.html:59
msgid "View"
msgstr ""

#: templates/mfa/index.html:65
msgid "Download"
msgstr ""

#: templates/mfa/index.html:73 templates/mfa/recovery_codes/generate.html:29
msgid "Generate"
msgstr ""

#: templates/mfa/messages/recovery_codes_generated.txt:2
msgid "A new set of recovery codes has been generated."
msgstr ""

#: templates/mfa/reauthenticate.html:5
#, fuzzy
#| msgid "token secret"
msgid "Enter an authenticator code:"
msgstr "\"token secret\"-a"

#: templates/mfa/recovery_codes/generate.html:9
msgid "You are about to generate a new set of recovery codes for your account."
msgstr ""

#: templates/mfa/recovery_codes/generate.html:11
msgid "This action will invalidate your existing codes."
msgstr ""

#: templates/mfa/recovery_codes/generate.html:13
msgid "Are you sure?"
msgstr ""

#: templates/mfa/recovery_codes/index.html:13
msgid "Unused codes"
msgstr ""

#: templates/mfa/recovery_codes/index.html:25
msgid "Download codes"
msgstr ""

#: templates/mfa/recovery_codes/index.html:30
msgid "Generate new codes"
msgstr ""

#: templates/mfa/totp/activate_form.html:4
#: templates/mfa/totp/activate_form.html:8
msgid "Activate Authenticator App"
msgstr ""

#: templates/mfa/totp/activate_form.html:11
msgid ""
"To protect your account with two-factor authentication, scan the QR code "
"below with your authenticator app. Then, input the verification code "
"generated by the app below."
msgstr ""

#: templates/mfa/totp/activate_form.html:21
#, fuzzy
#| msgid "token secret"
msgid "Authenticator secret"
msgstr "\"token secret\"-a"

#: templates/mfa/totp/activate_form.html:24
msgid ""
"You can store this secret and use it to reinstall your authenticator app at "
"a later time."
msgstr ""

#: templates/mfa/totp/deactivate_form.html:5
#: templates/mfa/totp/deactivate_form.html:9
msgid "Deactivate Authenticator App"
msgstr ""

#: templates/mfa/totp/deactivate_form.html:12
msgid ""
"You are about to deactivate authenticator app based authentication. Are you "
"sure?"
msgstr ""

#: templates/socialaccount/authentication_error.html:5
#: templates/socialaccount/authentication_error.html:9
#, fuzzy
#| msgid "Social Network Login Failure"
msgid "Third-Party Login Failure"
msgstr "Arazoak sare sozialarekin logina egitean"

#: templates/socialaccount/authentication_error.html:11
#, fuzzy
#| msgid ""
#| "An error occurred while attempting to login via your social network "
#| "account."
msgid ""
"An error occurred while attempting to login via your third-party account."
msgstr ""
"Arazoren bat izan da zure sare sozial kontua erabiltzen saioa hasteko "
"ahaleginean."

#: templates/socialaccount/connections.html:5
#: templates/socialaccount/connections.html:9
msgid "Account Connections"
msgstr "Lotutako kontuak"

#: templates/socialaccount/connections.html:13
#, fuzzy
#| msgid ""
#| "You can sign in to your account using any of the following third party "
#| "accounts:"
msgid ""
"You can sign in to your account using any of the following third-party "
"accounts:"
msgstr ""
"Ondorengo zerbitzu hauetako edozein erabili dezakezu zure kontuan sartzeko:"

#: templates/socialaccount/connections.html:45
#, fuzzy
#| msgid ""
#| "You currently have no social network accounts connected to this account."
msgid "You currently have no third-party accounts connected to this account."
msgstr "Oraingoz ez duzu sare sozial konturik lotu kontu honekin."

#: templates/socialaccount/connections.html:48
#, fuzzy
#| msgid "Add a 3rd Party Account"
msgid "Add a Third-Party Account"
msgstr "Sare sozial kontu bat gehitu"

#: templates/socialaccount/email/account_connected_message.txt:4
#, python-format
msgid ""
"A third-party account from %(provider)s has been connected to your account."
msgstr ""

#: templates/socialaccount/email/account_connected_subject.txt:3
#, fuzzy
#| msgid "Add a 3rd Party Account"
msgid "Third-Party Account Connected"
msgstr "Sare sozial kontu bat gehitu"

#: templates/socialaccount/email/account_disconnected_message.txt:4
#, python-format
msgid ""
"A third-party account from %(provider)s has been disconnected from your "
"account."
msgstr ""

#: templates/socialaccount/email/account_disconnected_subject.txt:3
#, fuzzy
#| msgid "Add a 3rd Party Account"
msgid "Third-Party Account Disconnected"
msgstr "Sare sozial kontu bat gehitu"

#: templates/socialaccount/login.html:10
#, python-format
msgid "Connect %(provider)s"
msgstr ""

#: templates/socialaccount/login.html:13
#, python-format
msgid "You are about to connect a new third-party account from %(provider)s."
msgstr ""

#: templates/socialaccount/login.html:17
#, python-format
msgid "Sign In Via %(provider)s"
msgstr ""

#: templates/socialaccount/login.html:20
#, python-format
msgid "You are about to sign in using a third-party account from %(provider)s."
msgstr ""

#: templates/socialaccount/login.html:27
msgid "Continue"
msgstr ""

#: templates/socialaccount/login_cancelled.html:5
#: templates/socialaccount/login_cancelled.html:9
msgid "Login Cancelled"
msgstr "Baliogabetutako logina"

#: templates/socialaccount/login_cancelled.html:13
#, python-format
msgid ""
"You decided to cancel logging in to our site using one of your existing "
"accounts. If this was a mistake, please proceed to <a "
"href=\"%(login_url)s\">sign in</a>."
msgstr ""
"Lotutako kontu batekin saioa hasteko saiakera bertan behera utzi duzu. "
"Oharkabean gertatu bada, mesedez <a href=\"%(login_url)s\">saioa hasi</a> "
"berriro."

#: templates/socialaccount/messages/account_connected.txt:2
#, fuzzy
#| msgid "The social account has been connected."
msgid "The third-party account has been connected."
msgstr "Sare sozial kontua behar bezala lotu da."

#: templates/socialaccount/messages/account_connected_other.txt:2
#, fuzzy
#| msgid "The social account is already connected to a different account."
msgid "The third-party account is already connected to a different account."
msgstr "Sare sozial kontua dagoeneko beste kontu bati lotuta dago."

#: templates/socialaccount/messages/account_disconnected.txt:2
#, fuzzy
#| msgid "The social account has been disconnected."
msgid "The third-party account has been disconnected."
msgstr "Sare sozial kontu honekin lotura ezabatu da."

#: templates/socialaccount/signup.html:12
#, python-format
msgid ""
"You are about to use your %(provider_name)s account to login to\n"
"%(site_name)s. As a final step, please complete the following form:"
msgstr ""
"Zure %(provider_name)s kontua erabiltzear zaude %(site_name)s\n"
"webgunean saioa hasteko. Azken pausu bezala, mesedez osa ezazu\n"
"formulario hau:"

#: templates/socialaccount/snippets/login.html:9
msgid "Or use a third-party"
msgstr ""

#: templates/usersessions/messages/sessions_logged_out.txt:2
msgid "Signed out of all other sessions."
msgstr ""

#: templates/usersessions/usersession_list.html:6
#: templates/usersessions/usersession_list.html:10
msgid "Sessions"
msgstr ""

#: templates/usersessions/usersession_list.html:23
msgid "Started At"
msgstr ""

#: templates/usersessions/usersession_list.html:24
#, fuzzy
#| msgid "Email Addresses"
msgid "IP Address"
msgstr "Helbide elektronikoak"

#: templates/usersessions/usersession_list.html:25
msgid "Browser"
msgstr ""

#: templates/usersessions/usersession_list.html:27
msgid "Last seen at"
msgstr ""

#: templates/usersessions/usersession_list.html:47
#, fuzzy
#| msgid "Current Password"
msgid "Current"
msgstr "Oraingo pasahitza"

#: templates/usersessions/usersession_list.html:60
msgid "Sign Out Other Sessions"
msgstr ""

#: usersessions/apps.py:9
msgid "User Sessions"
msgstr ""

#: usersessions/models.py:48
msgid "session key"
msgstr ""

#, fuzzy
#~| msgid "The following email addresses are associated with your account:"
#~ msgid "The following email address is associated with your account:"
#~ msgstr "Helbide elektroniko hauek zure kontuari lotuta daude:"

#, fuzzy
#~| msgid "Confirm Email Address"
#~ msgid "Change Email Address"
#~ msgstr "Helbide elektronikoa egiaztatu"

#, python-format
#~ msgid ""
#~ "Please sign in with one\n"
#~ "of your existing third party accounts. Or, <a "
#~ "href=\"%(signup_url)s\">sign up</a>\n"
#~ "for a %(site_name)s account and sign in below:"
#~ msgstr ""
#~ "Mesedez hasi saioa lotutako sare sozial kontu bat\n"
#~ "erabiliz, edo <a href=\"%(signup_url)s\">sortu kontu bat</a>\n"
#~ "%(site_name)s webgunean eta saioa hasi hemen:"

#~ msgid "or"
#~ msgstr "edo"

#~ msgid "change password"
#~ msgstr "pasahitza aldatu"

#~ msgid "OpenID Sign In"
#~ msgstr "OpenID-rekin sartu"

#~ msgid "This email address is already associated with another account."
#~ msgstr "Helbide elektroniko hau dagoeneko beste kontu bati lotuta dago."

#~ msgid ""
#~ "We have sent you an e-mail. Please contact us if you do not receive it "
#~ "within a few minutes."
#~ msgstr ""
#~ "Email bat bidali dizugu. Mesedez jarri gurekin kontaktuan hurrengo "
#~ "minutuetan jasotzen ez baduzu."

#~ msgid "Account"
#~ msgstr "Kontua"
