# -*- coding: utf-8 -*-
from allauth.socialaccount import app_settings
from allauth.socialaccount.adapter import get_adapter
from allauth.socialaccount.providers.auth0.provider import Auth0Provider
from allauth.socialaccount.providers.oauth2.views import (
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>pter,
    OAuth2CallbackView,
    OAuth2LoginView,
)


class Auth0OAuth2Adapter(OAuth2Adapter):
    provider_id = Auth0Provider.id
    supports_state = True

    settings = app_settings.PROVIDERS.get(provider_id, {})
    provider_base_url = settings.get("AUTH0_URL")

    access_token_url = "{0}/oauth/token".format(provider_base_url)
    authorize_url = "{0}/authorize".format(provider_base_url)
    profile_url = "{0}/userinfo".format(provider_base_url)

    def complete_login(self, request, app, token, response):
        extra_data = (
            get_adapter()
            .get_requests_session()
            .get(self.profile_url, params={"access_token": token.token})
            .json()
        )
        return self.get_provider().sociallogin_from_response(request, extra_data)


oauth2_login = OAuth2LoginView.adapter_view(Auth0OAuth2Adapter)
oauth2_callback = OAuth2CallbackView.adapter_view(Auth0OAuth2Adapter)
