# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-01-17 12:01-0600\n"
"PO-Revision-Date: 2024-01-28 12:36+0900\n"
"Last-Translator: k-brahma <<EMAIL>>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: ja-JP\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#: account/adapter.py:50
msgid "Username can not be used. Please use other username."
msgstr "このユーザー名は使用できません。他のユーザー名を選んでください。"

#: account/adapter.py:56
msgid "Too many failed login attempts. Try again later."
msgstr "ログイン失敗が連続しています。時間が経ってからやり直してください。"

#: account/adapter.py:58
msgid "A user is already registered with this email address."
msgstr "他のユーザーがこのメールアドレスを使用しています。"

#: account/adapter.py:59
msgid "Please type your current password."
msgstr "現在のパスワードを入力してください。"

#: account/adapter.py:60
#, fuzzy
#| msgid "Current Password"
msgid "Incorrect password."
msgstr "現在のパスワード"

#: account/adapter.py:61
#, python-brace-format
msgid "Password must be a minimum of {0} characters."
msgstr "パスワードは {0} 文字以上の長さが必要です。"

#: account/adapter.py:62
msgid "The email address is not assigned to any user account"
msgstr "このメールアドレスで登録されたユーザーアカウントがありません。"

#: account/adapter.py:725
msgid "Use your password"
msgstr "パスワードを使用する"

#: account/adapter.py:735
msgid "Use your authenticator app"
msgstr "認証アプリを利用する"

#: account/admin.py:23
#, fuzzy
#| msgid "Your primary email address must be verified."
msgid "Mark selected email addresses as verified"
msgstr "メインのメールアドレスは確認済みでなければいけません。"

#: account/apps.py:11
msgid "Accounts"
msgstr "アカウント"

#: account/forms.py:60 account/forms.py:449
msgid "You must type the same password each time."
msgstr "同じパスワードを入力してください。"

#: account/forms.py:92 account/forms.py:412 account/forms.py:554
#: account/forms.py:688
msgid "Password"
msgstr "パスワード"

#: account/forms.py:93
msgid "Remember Me"
msgstr "ログインしたままにする"

#: account/forms.py:97
msgid "This account is currently inactive."
msgstr "このアカウントは現在無効です。"

#: account/forms.py:99
msgid "The email address and/or password you specified are not correct."
msgstr "入力されたメールアドレスもしくはパスワードが正しくありません。"

#: account/forms.py:102
msgid "The username and/or password you specified are not correct."
msgstr "入力されたユーザー名もしくはパスワードが正しくありません。"

#: account/forms.py:112 account/forms.py:287 account/forms.py:477
#: account/forms.py:574
msgid "Email address"
msgstr "メールアドレス"

#: account/forms.py:116 account/forms.py:325 account/forms.py:474
#: account/forms.py:569
msgid "Email"
msgstr "メールアドレス"

#: account/forms.py:119 account/forms.py:122 account/forms.py:277
#: account/forms.py:280
msgid "Username"
msgstr "ユーザー名"

#: account/forms.py:132
msgid "Username or email"
msgstr "ユーザー名またはメールアドレス"

#: account/forms.py:135
msgctxt "field label"
msgid "Login"
msgstr "ログイン"

#: account/forms.py:146
#| msgid "Forgot Password?"
msgid "Forgot your password?"
msgstr "パスワードをお忘れですか？"

#: account/forms.py:316
msgid "Email (again)"
msgstr "メールアドレス（確認用）"

#: account/forms.py:320
#| msgid "email confirmation"
msgid "Email address confirmation"
msgstr "メールアドレスの確認"

#: account/forms.py:328
msgid "Email (optional)"
msgstr "メールアドレス（オプション）"

#: account/forms.py:383
#| msgid "You must type the same password each time."
msgid "You must type the same email each time."
msgstr "同じパスワードを入力してください。"

#: account/forms.py:418 account/forms.py:557
msgid "Password (again)"
msgstr "パスワード（再入力）"

#: account/forms.py:489
msgid "This email address is already associated with this account."
msgstr "このメールアドレスはすでに登録されています。"

#: account/forms.py:491
#, fuzzy, python-format
#| msgid "Your account has no verified email address."
msgid "You cannot add more than %d email addresses."
msgstr "確認済みのメールアドレスの登録が必要です。"

#: account/forms.py:529
msgid "Current Password"
msgstr "現在のパスワード"

#: account/forms.py:532 account/forms.py:637
msgid "New Password"
msgstr "新しいパスワード"

#: account/forms.py:535 account/forms.py:638
msgid "New Password (again)"
msgstr "新しいパスワード（再入力）"

#: account/forms.py:658
msgid "The password reset token was invalid."
msgstr "パスワードリセットトークンが無効です。"

#: account/models.py:21
msgid "user"
msgstr "ユーザー"

#: account/models.py:26 account/models.py:34 account/models.py:138
msgid "email address"
msgstr "メールアドレス"

#: account/models.py:28
msgid "verified"
msgstr "確認済み"

#: account/models.py:29
msgid "primary"
msgstr "メイン"

#: account/models.py:35
msgid "email addresses"
msgstr "メールアドレス"

#: account/models.py:141
msgid "created"
msgstr "作成日時"

#: account/models.py:142
msgid "sent"
msgstr "送信日時"

#: account/models.py:143 socialaccount/models.py:63
msgid "key"
msgstr "キー"

#: account/models.py:148
msgid "email confirmation"
msgstr "メールアドレスの確認"

#: account/models.py:149
msgid "email confirmations"
msgstr "メールアドレスの確認"

#: mfa/adapter.py:20
msgid ""
"You cannot activate two-factor authentication until you have verified your "
"email address."
msgstr ""
"メールアドレスの確認が完了するまで、二要素認証を有効にすることはできません。"

#: mfa/adapter.py:23
msgid ""
"You cannot add an email address to an account protected by two-factor "
"authentication."
msgstr ""
"二要素認証で保護されているアカウントには、メールアドレスを追加できません。"

#: mfa/adapter.py:25
msgid "Incorrect code."
msgstr "コードが正しくありません。"

#: mfa/adapter.py:27
msgid "You cannot deactivate two-factor authentication."
msgstr ""

#: mfa/apps.py:9
msgid "MFA"
msgstr ""

#: mfa/forms.py:15 mfa/forms.py:17 mfa/forms.py:52
msgid "Code"
msgstr ""

#: mfa/forms.py:50
msgid "Authenticator code"
msgstr "認証アプリコード"

#: mfa/models.py:19
msgid "Recovery codes"
msgstr ""

#: mfa/models.py:20
msgid "TOTP Authenticator"
msgstr ""

#: socialaccount/adapter.py:31
#, fuzzy, python-format
#| msgid ""
#| "An account already exists with this e-mail address. Please sign in to "
#| "that account first, then connect your %s account."
msgid ""
"An account already exists with this email address. Please sign in to that "
"account first, then connect your %s account."
msgstr ""
"このメールアドレスを使用するアカウントが既にあります。そのアカウントにログイ"
"ンしてから%sアカウントを接続してください"

#: socialaccount/adapter.py:148
msgid "Your account has no password set up."
msgstr "アカウントにパスワードを設定する必要があります。"

#: socialaccount/adapter.py:155
msgid "Your account has no verified email address."
msgstr "確認済みのメールアドレスの登録が必要です。"

#: socialaccount/apps.py:9
msgid "Social Accounts"
msgstr "外部アカウント"

#: socialaccount/models.py:37 socialaccount/models.py:91
msgid "provider"
msgstr "プロバイダー"

#: socialaccount/models.py:46
#| msgid "provider"
msgid "provider ID"
msgstr "プロバイダー ID"

#: socialaccount/models.py:50
msgid "name"
msgstr "ユーザー名"

#: socialaccount/models.py:52
msgid "client id"
msgstr "ユーザID"

#: socialaccount/models.py:54
msgid "App ID, or consumer key"
msgstr "App IDもしくはコンシューマキー"

#: socialaccount/models.py:57
msgid "secret key"
msgstr "シークレットキー"

#: socialaccount/models.py:60
msgid "API secret, client secret, or consumer secret"
msgstr ""
"APIシークレット、クライアントシークレット、またはコンシューマーシークレット"

#: socialaccount/models.py:63
msgid "Key"
msgstr "キー"

#: socialaccount/models.py:75
msgid "social application"
msgstr "ソーシャルアプリケーション"

#: socialaccount/models.py:76
msgid "social applications"
msgstr "ソーシャルアプリケーション"

#: socialaccount/models.py:111
msgid "uid"
msgstr ""

#: socialaccount/models.py:113
msgid "last login"
msgstr "最終ログイン"

#: socialaccount/models.py:114
msgid "date joined"
msgstr "アカウント作成日"

#: socialaccount/models.py:115
msgid "extra data"
msgstr "エクストラデータ"

#: socialaccount/models.py:119
msgid "social account"
msgstr "ソーシャルアカウント"

#: socialaccount/models.py:120
msgid "social accounts"
msgstr "ソーシャルアカウント"

#: socialaccount/models.py:154
msgid "token"
msgstr "トークン"

#: socialaccount/models.py:155
msgid "\"oauth_token\" (OAuth1) or access token (OAuth2)"
msgstr "\"oauth_token\" (OAuth1) もしくは Access Token (OAuth2)"

#: socialaccount/models.py:159
msgid "token secret"
msgstr "トークンシークレット"

#: socialaccount/models.py:160
msgid "\"oauth_token_secret\" (OAuth1) or refresh token (OAuth2)"
msgstr "\"oauth_token_secret\" (OAuth1) もしくは Refresh Token (OAuth2)"

#: socialaccount/models.py:163
msgid "expires at"
msgstr "失効期限"

#: socialaccount/models.py:168
msgid "social application token"
msgstr "ソーシャルアプリケーショントークン"

#: socialaccount/models.py:169
msgid "social application tokens"
msgstr "ソーシャルアプリケーショントークン"

#: socialaccount/providers/douban/views.py:37
msgid "Invalid profile data"
msgstr "無効なプロファイルデータ"

#: socialaccount/providers/oauth/client.py:85
#, fuzzy, python-format
#| msgid "Invalid response while obtaining request token from \"%s\"."
msgid ""
"Invalid response while obtaining request token from \"%s\". Response was: %s."
msgstr ""
"不正なレスポンスが返されたため、 \"%s\" からリクエストトークンを取得できませ"
"んでした。"

#: socialaccount/providers/oauth/client.py:119
#: socialaccount/providers/pocket/client.py:86
#, python-format
msgid "Invalid response while obtaining access token from \"%s\"."
msgstr ""
"不正なレスポンスが返されたため、 \"%s\" からアクセストークンを取得できません"
"でした。"

#: socialaccount/providers/oauth/client.py:140
#, python-format
msgid "No request token saved for \"%s\"."
msgstr "\"%s\" のリクエストトークンを保存できませんでした。"

#: socialaccount/providers/oauth/client.py:191
#, python-format
msgid "No access token saved for \"%s\"."
msgstr "\"%s\" のアクセストークンを保存できませんでした。"

#: socialaccount/providers/oauth/client.py:212
#, python-format
msgid "No access to private resources at \"%s\"."
msgstr "\"%s\" の情報にアクセスできませんでした。"

#: socialaccount/providers/pocket/client.py:41
#, python-format
msgid "Invalid response while obtaining request token from \"%s\"."
msgstr ""
"不正なレスポンスが返されたため、 \"%s\" からリクエストトークンを取得できませ"
"んでした。"

#: templates/account/account_inactive.html:5
#: templates/account/account_inactive.html:9
msgid "Account Inactive"
msgstr "無効なアカウント"

#: templates/account/account_inactive.html:11
msgid "This account is inactive."
msgstr "このアカウントは無効です。"

#: templates/account/base_reauthenticate.html:5
#: templates/account/base_reauthenticate.html:9
msgid "Confirm Access"
msgstr "アクセス確認"

#: templates/account/base_reauthenticate.html:11
msgid "Please reauthenticate to safeguard your account."
msgstr "アカウントを保護するために再認証してください。"

#: templates/account/base_reauthenticate.html:17
msgid "Alternative options"
msgstr "そのほかの方法"

#: templates/account/email.html:4 templates/account/email.html:8
msgid "Email Addresses"
msgstr "メールアドレス"

#: templates/account/email.html:11
msgid "The following email addresses are associated with your account:"
msgstr "以下のメールアドレスがアカウントに登録されています："

#: templates/account/email.html:23
msgid "Verified"
msgstr "確認済み"

#: templates/account/email.html:27
msgid "Unverified"
msgstr "未確認"

#: templates/account/email.html:32
msgid "Primary"
msgstr "メイン"

#: templates/account/email.html:42
msgid "Make Primary"
msgstr "メインにする"

#: templates/account/email.html:45 templates/account/email_change.html:37
msgid "Re-send Verification"
msgstr "確認メールを再送する"

#: templates/account/email.html:48 templates/socialaccount/connections.html:40
msgid "Remove"
msgstr "削除"

#: templates/account/email.html:57
msgid "Add Email Address"
msgstr "メールアドレスの登録"

#: templates/account/email.html:70
msgid "Add Email"
msgstr "メールアドレスの登録"

#: templates/account/email.html:79
msgid "Do you really want to remove the selected email address?"
msgstr "選択されたメールアドレスを削除してもよろしいですか？"

#: templates/account/email/account_already_exists_message.txt:4
#, python-format
msgid ""
"You are receiving this email because you or someone else tried to signup for "
"an\n"
"account using email address:\n"
"\n"
"%(email)s\n"
"\n"
"However, an account using that email address already exists.  In case you "
"have\n"
"forgotten about this, please use the password forgotten procedure to "
"recover\n"
"your account:\n"
"\n"
"%(password_reset_url)s"
msgstr ""

#: templates/account/email/account_already_exists_subject.txt:3
msgid "Account Already Exists"
msgstr ""

#: templates/account/email/base_message.txt:1
#, python-format
msgid "Hello from %(site_name)s!"
msgstr "こんにちは、%(site_name)sです。"

#: templates/account/email/base_message.txt:5
#, python-format
msgid ""
"Thank you for using %(site_name)s!\n"
"%(site_domain)s"
msgstr ""
"%(site_name)sを利用いただきありがとうございます!\n"
"%(site_domain)s"

#: templates/account/email/base_notification.txt:5
msgid ""
"You are receiving this mail because the following change was made to your "
"account:"
msgstr ""

#: templates/account/email/base_notification.txt:10
#, python-format
msgid ""
"If you do not recognize this change then please take proper security "
"precautions immediately. The change to your account originates from:\n"
"\n"
"- IP address: %(ip)s\n"
"- Browser: %(user_agent)s\n"
"- Date: %(timestamp)s"
msgstr ""

#: templates/account/email/email_changed_message.txt:4
#, python-format
msgid "Your email has been changed from %(from_email)s to %(to_email)s."
msgstr ""

#: templates/account/email/email_changed_subject.txt:3
#| msgid "Email address"
msgid "Email Changed"
msgstr "メールアドレスが変更されました"

#: templates/account/email/email_confirm_message.txt:4
#| msgid "You have confirmed %(email)s."
msgid "Your email has been confirmed."
msgstr "あなたのメールアドレスは確認されました。"

#: templates/account/email/email_confirm_subject.txt:3
#| msgid "email confirmation"
msgid "Email Confirmation"
msgstr "メールアドレスの確認"

#: templates/account/email/email_confirmation_message.txt:5
msgid ""
"You're receiving this email because user %(user_display)s has given your "
"email address to register an account on %(site_domain)s.\n"
"\n"
"To confirm this is correct, go to %(activate_url)s"
msgstr ""
"%(user_display)s さんが %(site_domain)s にこのメールアドレスを登録しようとしています。\n"
"問題がなければ、確認のために以下のURLをクリックしてください。\n"
"%(activate_url)s"

#: templates/account/email/email_confirmation_subject.txt:3
msgid "Please Confirm Your Email Address"
msgstr "メールアドレスを確認してください"

#: templates/account/email/email_deleted_message.txt:4
#, python-format
msgid "Email address %(deleted_email)s has been removed from your account."
msgstr "メールアドレス %(deleted_email)s はあなたのアカウントから削除されました。"

#: templates/account/email/email_deleted_subject.txt:3
#| msgid "Remove"
msgid "Email Removed"
msgstr "メールアドレスの削除"

#: templates/account/email/password_changed_message.txt:4
#| msgid "Your password is now changed."
msgid "Your password has been changed."
msgstr "パスワードが変更されました。"

#: templates/account/email/password_changed_subject.txt:3
#| msgid "Password (again)"
msgid "Password Changed"
msgstr "パスワードが変更されました"

#: templates/account/email/password_reset_key_message.txt:4
#| msgid ""
#| "You're receiving this e-mail because you or someone else has requested a "
#| "password for your user account.\n"
#| "It can be safely ignored if you did not request a password reset. Click "
#| "the link below to reset your password."
msgid ""
"You're receiving this email because you or someone else has requested a "
"password reset for your user account.\n"
"It can be safely ignored if you did not request a password reset. Click the "
"link below to reset your password."
msgstr ""
"このメールは、あなた（もしくは別の誰か）がパスワードの再設定を行おうとしたた"
"めに送られました。\n"
"パスワードの再設定を要求したのがあなたではない場合、このメールは無視してくだ"
"さい。パスワードを再設定するためには、以下のリンクをクリックしてください。"

#: templates/account/email/password_reset_key_message.txt:9
#, python-format
msgid "In case you forgot, your username is %(username)s."
msgstr "あなたのアカウント（ユーザー名）は %(username)s です。"

#: templates/account/email/password_reset_key_subject.txt:3
#: templates/account/email/unknown_account_subject.txt:3
msgid "Password Reset Email"
msgstr "パスワード再設定メール"

#: templates/account/email/password_reset_message.txt:4
#| msgid "Your password is now changed."
msgid "Your password has been reset."
msgstr "パスワードがリセットされました。"

#: templates/account/email/password_reset_subject.txt:3
#: templates/account/password_reset.html:4
#: templates/account/password_reset.html:8
#: templates/account/password_reset_done.html:6
#: templates/account/password_reset_done.html:10
msgid "Password Reset"
msgstr "パスワード再設定"

#: templates/account/email/password_set_message.txt:4
#| msgid "Your password is now changed."
msgid "Your password has been set."
msgstr "パスワードが設定されました。"

#: templates/account/email/password_set_subject.txt:3
#| msgid "Password Reset"
msgid "Password Set"
msgstr "パスワード再設定"

#: templates/account/email/unknown_account_message.txt:4
#| msgid ""
#| "You're receiving this e-mail because you or someone else has requested a "
#| "password for your user account.\n"
#| "It can be safely ignored if you did not request a password reset. Click "
#| "the link below to reset your password."
msgid ""
"You are receiving this email because you or someone else has requested a\n"
"password for your user account. However, we do not have any record of a "
"user\n"
"with email %(email)s in our database.\n"
"\n"
"This mail can be safely ignored if you did not request a password reset.\n"
"\n"
"If it was you, you can sign up for an account using the link below."
msgstr ""
"このメールは、あなた（もしくは別の誰か）がパスワードの再設定を行おうとしたた"
"めに送られました。\n"
"パスワードの再設定を要求したのがあなたではない場合、このメールは無視してくだ"
"さい。パスワードを再設定するためには、以下のリンクをクリックしてください。"

#: templates/account/email_change.html:5 templates/account/email_change.html:9
#| msgid "Email Addresses"
msgid "Email Address"
msgstr "メールアドレス"

#: templates/account/email_change.html:21
#: templates/account/email_change.html:29
#| msgid "Current Password"
msgid "Current email"
msgstr "現在のメールアドレス"

#: templates/account/email_change.html:31
msgid "Changing to"
msgstr ""

#: templates/account/email_change.html:35
#| msgid "Your primary email address must be verified."
msgid "Your email address is still pending verification."
msgstr "あなたのメールアドレスはまだ確認されていません。"

#: templates/account/email_change.html:41
msgid "Cancel Change"
msgstr ""

#: templates/account/email_change.html:49
#, fuzzy
#| msgid "Email"
msgid "Change to"
msgstr "メールアドレス"

#: templates/account/email_change.html:55
#: templates/allauth/layouts/base.html:29
#| msgid "Email"
msgid "Change Email"
msgstr "メールアドレス変更"

#: templates/account/email_confirm.html:6
#: templates/account/email_confirm.html:10
msgid "Confirm Email Address"
msgstr "メールアドレスの確認"

#: templates/account/email_confirm.html:16
msgid ""
"Please confirm that <a href=\"mailto:%(email)s\">%(email)s</a> is an email "
"address for user %(user_display)s."
msgstr ""
"メールアドレス <a href=\"mailto:%(email)s\">%(email)s</a> がユーザー "
"%(user_display)s さんのものであることを確認してください。"

#: templates/account/email_confirm.html:23
#: templates/account/reauthenticate.html:20
#: templates/mfa/reauthenticate.html:20
msgid "Confirm"
msgstr "確認する"

#: templates/account/email_confirm.html:29
#: templates/account/messages/email_confirmation_failed.txt:2
#, fuzzy, python-format
#| msgid "The social account is already connected to a different account."
msgid ""
"Unable to confirm %(email)s because it is already confirmed by a different "
"account."
msgstr "この外部アカウントは他のアカウントにリンクされています。"

#: templates/account/email_confirm.html:35
#, fuzzy, python-format
#| msgid ""
#| "This e-mail confirmation link expired or is invalid. Please <a "
#| "href=\"%(email_url)s\">issue a new e-mail confirmation request</a>."
msgid ""
"This email confirmation link expired or is invalid. Please <a "
"href=\"%(email_url)s\">issue a new email confirmation request</a>."
msgstr ""
"メールアドレス確認用のリンクが不正か、期限が切れています。<a "
"href=\"%(email_url)s\">確認用のメールを再送</a>してください。"

#: templates/account/login.html:5 templates/account/login.html:9
#: templates/account/login.html:29 templates/allauth/layouts/base.html:36
#: templates/mfa/authenticate.html:5 templates/mfa/authenticate.html:23
#: templates/openid/login.html:5 templates/openid/login.html:9
#: templates/openid/login.html:20 templates/socialaccount/login.html:5
msgid "Sign In"
msgstr "ログイン"

#: templates/account/login.html:12
msgid ""
"If you have not created an account yet, then please\n"
"    <a href=\"%(signup_url)s\">sign up</a> first."
msgstr ""
"アカウントをまだお持ちでなければ、こちらから <a href=\"%(signup_url)s\">ユーザー登録</a> してください。"

#: templates/account/logout.html:4 templates/account/logout.html:8
#: templates/account/logout.html:23 templates/allauth/layouts/base.html:32
#: templates/usersessions/usersession_list.html:64
msgid "Sign Out"
msgstr "ログアウト"

#: templates/account/logout.html:10
msgid "Are you sure you want to sign out?"
msgstr "ログアウトしますか？"

#: templates/account/messages/cannot_delete_primary_email.txt:2
#, python-format
msgid "You cannot remove your primary email address (%(email)s)."
msgstr "メインのメールアドレス（%(email)s）を削除することはできません。"

#: templates/account/messages/email_confirmation_sent.txt:2
#, python-format
msgid "Confirmation email sent to %(email)s."
msgstr "%(email)s に確認メールを送信しました。"

#: templates/account/messages/email_confirmed.txt:2
#, python-format
msgid "You have confirmed %(email)s."
msgstr "%(email)s は確認されました。"

#: templates/account/messages/email_deleted.txt:2
#, python-format
msgid "Removed email address %(email)s."
msgstr "メールアドレス %(email)s を削除しました。"

#: templates/account/messages/logged_in.txt:4
#, python-format
msgid "Successfully signed in as %(name)s."
msgstr "ユーザー %(name)s としてログインしました。"

#: templates/account/messages/logged_out.txt:2
msgid "You have signed out."
msgstr "ログアウトしました。"

#: templates/account/messages/password_changed.txt:2
msgid "Password successfully changed."
msgstr "パスワードが変更されました。"

#: templates/account/messages/password_set.txt:2
msgid "Password successfully set."
msgstr "パスワードが設定されました。"

#: templates/account/messages/primary_email_set.txt:2
msgid "Primary email address set."
msgstr "メインのメールアドレスが設定されました。"

#: templates/account/messages/unverified_primary_email.txt:2
msgid "Your primary email address must be verified."
msgstr "メインのメールアドレスは確認済みでなければいけません。"

#: templates/account/password_change.html:4
#: templates/account/password_change.html:8
#: templates/account/password_change.html:19
#: templates/account/password_reset_from_key.html:5
#: templates/account/password_reset_from_key.html:12
#: templates/account/password_reset_from_key.html:29
#: templates/account/password_reset_from_key_done.html:5
#: templates/account/password_reset_from_key_done.html:9
msgid "Change Password"
msgstr "パスワード変更"

#: templates/account/password_change.html:21
msgid "Forgot Password?"
msgstr "パスワードをお忘れですか？"

#: templates/account/password_reset.html:14
msgid ""
"Forgotten your password? Enter your email address below, and we'll send you "
"an email allowing you to reset it."
msgstr ""
"パスワードをお忘れですか？パスワードをリセットするために、メールアドレスを入力してください。"

#: templates/account/password_reset.html:25
msgid "Reset My Password"
msgstr "パスワードをリセット"

#: templates/account/password_reset.html:29
msgid "Please contact us if you have any trouble resetting your password."
msgstr "パスワードの再設定に問題がある場合はご連絡ください。"

#: templates/account/password_reset_done.html:16
msgid ""
"We have sent you an email. If you have not received it please check your "
"spam folder. Otherwise contact us if you do not receive it in a few minutes."
msgstr ""
"確認のためのメールを送信しました。受信できない場合は、スパムフォルダをご確認ください。"
"数分以内にメールが届かない場合はご連絡ください。"

#: templates/account/password_reset_from_key.html:10
msgid "Bad Token"
msgstr "不正なトークン"

#: templates/account/password_reset_from_key.html:18
#, python-format
msgid ""
"The password reset link was invalid, possibly because it has already been "
"used.  Please request a <a href=\"%(passwd_reset_url)s\">new password reset</"
"a>."
msgstr ""
"パスワード再設定用のリンクが不正です。すでに使用された可能性があります。もう"
"一度 <a href=\"%(passwd_reset_url)s\">パスワードの再設定</a>をお試しくださ"
"い。"

#: templates/account/password_reset_from_key_done.html:11
msgid "Your password is now changed."
msgstr "パスワードが変更されました。"

#: templates/account/password_set.html:5 templates/account/password_set.html:9
#: templates/account/password_set.html:20
msgid "Set Password"
msgstr "パスワード設定"

#: templates/account/reauthenticate.html:5
msgid "Enter your password:"
msgstr "パスワードを入力してください。"

#: templates/account/signup.html:4 templates/socialaccount/signup.html:5
msgid "Signup"
msgstr "ユーザー登録"

#: templates/account/signup.html:8 templates/account/signup.html:27
#: templates/allauth/layouts/base.html:39 templates/socialaccount/signup.html:9
#: templates/socialaccount/signup.html:29
msgid "Sign Up"
msgstr "ユーザー登録"

#: templates/account/signup.html:11
#, python-format
msgid ""
"Already have an account? Then please <a href=\"%(login_url)s\">sign in</a>."
msgstr ""
"すでにアカウントをお持ちであれば、こちらから <a href=\"%(login_url)s\">ログイ"
"ン</a> してください。"

#: templates/account/signup_closed.html:5
#: templates/account/signup_closed.html:9
msgid "Sign Up Closed"
msgstr "ユーザー登録停止中"

#: templates/account/signup_closed.html:11
msgid "We are sorry, but the sign up is currently closed."
msgstr "申し訳ありません、現在ユーザー登録を停止しています。"

#: templates/account/snippets/already_logged_in.html:7
msgid "Note"
msgstr "注意"

#: templates/account/snippets/already_logged_in.html:7
#| msgid "you are already logged in as %(user_display)s."
msgid "You are already logged in as %(user_display)s."
msgstr "%(user_display)s さんとしてすでにログイン中です。"

#: templates/account/snippets/warn_no_email.html:3
msgid "Warning:"
msgstr "注意："

#: templates/account/snippets/warn_no_email.html:3
msgid ""
"You currently do not have any email address set up. You should really add an "
"email address so you can receive notifications, reset your password, etc."
msgstr ""
"メールアドレスが設定されていません。通知を受け取ったり、パスワードをリセットしたりするためには"
"メールアドレスを登録する必要があります。"

#: templates/account/verification_sent.html:5
#: templates/account/verification_sent.html:9
#: templates/account/verified_email_required.html:5
#: templates/account/verified_email_required.html:9
msgid "Verify Your Email Address"
msgstr "メールアドレスを確認してください"

#: templates/account/verification_sent.html:12
msgid ""
"We have sent an email to you for verification. Follow the link provided to "
"finalize the signup process. If you do not see the verification email in "
"your main inbox, check your spam folder. Please contact us if you do not "
"receive the verification email within a few minutes."
msgstr ""
"確認のメールを送信しました。メールに記載されたリンクをクリックして、ユーザー"
"登録を完了させてください。数分待っても確認のメールが届かない場合はご連絡くだ"
"さい。"

#: templates/account/verified_email_required.html:13
#, fuzzy
#| msgid ""
#| "This part of the site requires us to verify that\n"
#| "you are who you claim to be. For this purpose, we require that you\n"
#| "verify ownership of your e-mail address. "
msgid ""
"This part of the site requires us to verify that\n"
"you are who you claim to be. For this purpose, we require that you\n"
"verify ownership of your email address. "
msgstr ""
"このページにアクセスするためには、本人確認が必要です。\n"
"そのために、登録されているメールアドレスがご自身のものであることを確認してい"
"ただきます。"

#: templates/account/verified_email_required.html:18
msgid ""
"We have sent an email to you for\n"
"verification. Please click on the link inside that email. If you do not see "
"the verification email in your main inbox, check your spam folder. "
"Otherwise\n"
"contact us if you do not receive it within a few minutes."
msgstr ""
"確認のためのメールを送信しました。メールに記載されたリンクをクリックしてください。\n"
"確認メールが受信ボックスに見つからない場合は、スパムフォルダをチェックしてください。"
"数分以内にメールが届かない場合はご連絡ください。"

#: templates/account/verified_email_required.html:23
msgid ""
"<strong>Note:</strong> you can still <a href=\"%(email_url)s\">change your "
"email address</a>."
msgstr ""
"<strong>注意:</strong> <a href=\"%(email_url)s\">メールアドレスの変更</a>をし"
"ていただくことも可能です。"

#: templates/allauth/layouts/base.html:18
msgid "Messages:"
msgstr "メッセージ"

#: templates/allauth/layouts/base.html:25
msgid "Menu:"
msgstr "メニュー"

#: templates/mfa/authenticate.html:9 templates/mfa/index.html:5
#: templates/mfa/index.html:9
msgid "Two-Factor Authentication"
msgstr "二段階認証"

#: templates/mfa/authenticate.html:12
msgid ""
"Your account is protected by two-factor authentication. Please enter an "
"authenticator code:"
msgstr ""
"あなたのアカウントは二段階認証で保護されています。"
"認証コードを入力してください:"

#: templates/mfa/authenticate.html:27
msgid "Cancel"
msgstr "キャンセル"

#: templates/mfa/email/recovery_codes_generated_message.txt:4
msgid ""
"A new set of Two-Factor Authentication recovery codes has been generated."
msgstr "二段階認証用のリカバリーコードを新たに生成しました。"

#: templates/mfa/email/recovery_codes_generated_subject.txt:3
msgid "New Recovery Codes Generated"
msgstr "二段階認証用のリカバリーコードを生成しました。"

#: templates/mfa/email/totp_activated_message.txt:4
#: templates/mfa/messages/totp_activated.txt:2
msgid "Authenticator app activated."
msgstr "認証アプリが有効化されました。"

#: templates/mfa/email/totp_activated_subject.txt:3
msgid "Authenticator App Activated"
msgstr "認証アプリが有効化されました。"

#: templates/mfa/email/totp_deactivated_message.txt:4
#: templates/mfa/messages/totp_deactivated.txt:2
msgid "Authenticator app deactivated."
msgstr "認証アプリが無効化されました。"

#: templates/mfa/email/totp_deactivated_subject.txt:3
#, fuzzy
#| msgid "token secret"
msgid "Authenticator App Deactivated"
msgstr "トークンシークレット"

#: templates/mfa/index.html:14 templates/mfa/totp/base.html:4
msgid "Authenticator App"
msgstr "認証アプリ"

#: templates/mfa/index.html:18
msgid "Authentication using an authenticator app is active."
msgstr "認証アプリを使用した認証が有効です。"

#: templates/mfa/index.html:20
msgid "An authenticator app is not active."
msgstr "認証アプリは有効ではありません。"

#: templates/mfa/index.html:28 templates/mfa/totp/deactivate_form.html:24
msgid "Deactivate"
msgstr "無効化する"

#: templates/mfa/index.html:32 templates/mfa/totp/activate_form.html:32
msgid "Activate"
msgstr "有効化する"

#: templates/mfa/index.html:42 templates/mfa/recovery_codes/base.html:4
#: templates/mfa/recovery_codes/generate.html:6
#: templates/mfa/recovery_codes/index.html:6
msgid "Recovery Codes"
msgstr "リカバリーコード"

#: templates/mfa/index.html:47 templates/mfa/recovery_codes/index.html:9
#, python-format
msgid ""
"There is %(unused_count)s out of %(total_count)s recovery codes available."
msgid_plural ""
"There are %(unused_count)s out of %(total_count)s recovery codes available."
msgstr[0] ""
"利用可能なリカバリーコードは%(unused_count)s個中%(total_count)s個です。"
msgstr[1] ""
"利用可能なリカバリーコードは%(unused_count)s個中%(total_count)s個です。"

#: templates/mfa/index.html:50
msgid "No recovery codes set up."
msgstr "リカバリーコードがセットアップされていません。"

#: templates/mfa/index.html:59
msgid "View"
msgstr ""

#: templates/mfa/index.html:65
msgid "Download"
msgstr "ダウンロード"

#: templates/mfa/index.html:73 templates/mfa/recovery_codes/generate.html:29
msgid "Generate"
msgstr "生成する"

#: templates/mfa/messages/recovery_codes_generated.txt:2
msgid "A new set of recovery codes has been generated."
msgstr "新しいリカバリーコードセットが生成されました。"

#: templates/mfa/reauthenticate.html:5
msgid "Enter an authenticator code:"
msgstr "認証アプリのコードを入力する"

#: templates/mfa/recovery_codes/generate.html:9
msgid "You are about to generate a new set of recovery codes for your account."
msgstr "新しいリカバリーコードセットを生成しようとしています。"

#: templates/mfa/recovery_codes/generate.html:11
msgid "This action will invalidate your existing codes."
msgstr "この操作により、現在のコードが無効になります。"

#: templates/mfa/recovery_codes/generate.html:13
msgid "Are you sure?"
msgstr "本当によろしいですか？"

#: templates/mfa/recovery_codes/index.html:13
msgid "Unused codes"
msgstr "未使用のコード"

#: templates/mfa/recovery_codes/index.html:25
msgid "Download codes"
msgstr "コードをダウンロードする"

#: templates/mfa/recovery_codes/index.html:30
msgid "Generate new codes"
msgstr "新しいコードを生成する"

#: templates/mfa/totp/activate_form.html:4
#: templates/mfa/totp/activate_form.html:8
msgid "Activate Authenticator App"
msgstr "認証アプリを有効化する"

#: templates/mfa/totp/activate_form.html:11
msgid ""
"To protect your account with two-factor authentication, scan the QR code "
"below with your authenticator app. Then, input the verification code "
"generated by the app below."
msgstr ""
"アカウントを二要素認証で保護するために、認証アプリで以下のQRコードをスキャン"
"してください。次に、アプリで生成された確認コードを以下に入力してください。"

#: templates/mfa/totp/activate_form.html:21
msgid "Authenticator secret"
msgstr "トークンシークレット"

#: templates/mfa/totp/activate_form.html:24
msgid ""
"You can store this secret and use it to reinstall your authenticator app at "
"a later time."
msgstr ""
"このシークレットを保存し、後でオーセンティケーターアプリを再インストールする"
"際に使用できます。"

#: templates/mfa/totp/deactivate_form.html:5
#: templates/mfa/totp/deactivate_form.html:9
msgid "Deactivate Authenticator App"
msgstr "認証アプリを無効化する"

#: templates/mfa/totp/deactivate_form.html:12
msgid ""
"You are about to deactivate authenticator app based authentication. Are you "
"sure?"
msgstr "認証アプリによる認証を無効化しようとしています。本当によろしいですか。"

#: templates/socialaccount/authentication_error.html:5
#: templates/socialaccount/authentication_error.html:9
#| msgid "Social Network Login Failure"
msgid "Third-Party Login Failure"
msgstr "ソーシャルログインに失敗しました"

#: templates/socialaccount/authentication_error.html:11
msgid ""
"An error occurred while attempting to login via your third-party account."
msgstr ""
"ソーシャルアカウントでのログイン時ににエラーが発生しました。"

#: templates/socialaccount/connections.html:5
#: templates/socialaccount/connections.html:9
msgid "Account Connections"
msgstr "アカウントリンク"

#: templates/socialaccount/connections.html:13
msgid ""
"You can sign in to your account using any of the following third-party "
"accounts:"
msgstr "以下の外部アカウントを使ってログインすることができます："

#: templates/socialaccount/connections.html:45
msgid "You currently have no third-party accounts connected to this account."
msgstr "あなたのカウントに結びつけられた外部アカウントはありません。"

#: templates/socialaccount/connections.html:48
msgid "Add a Third-Party Account"
msgstr "外部アカウントを追加する"

#: templates/socialaccount/email/account_connected_message.txt:4
#, python-format
msgid ""
"A third-party account from %(provider)s has been connected to your account."
msgstr ""

#: templates/socialaccount/email/account_connected_subject.txt:3
#, fuzzy
#| msgid "Add a 3rd Party Account"
msgid "Third-Party Account Connected"
msgstr "外部アカウントを追加する"

#: templates/socialaccount/email/account_disconnected_message.txt:4
#, python-format
msgid ""
"A third-party account from %(provider)s has been disconnected from your "
"account."
msgstr ""

#: templates/socialaccount/email/account_disconnected_subject.txt:3
msgid "Third-Party Account Disconnected"
msgstr "外部アカウントは接続解除されました"

#: templates/socialaccount/login.html:10
#, python-format
msgid "Connect %(provider)s"
msgstr "%(provider)sと連携する"

#: templates/socialaccount/login.html:13
msgid "You are about to connect a new third-party account from %(provider)s."
msgstr ""
"%(provider)sの新しいサードパーティーアカウントを連携しようとしています。"

#: templates/socialaccount/login.html:17
#, python-format
msgid "Sign In Via %(provider)s"
msgstr "%(provider)sでログインする"

#: templates/socialaccount/login.html:20
msgid "You are about to sign in using a third-party account from %(provider)s."
msgstr "%(provider)sのサードパーティーアカウントを使用してログインしようとしています。"

#: templates/socialaccount/login.html:27
msgid "Continue"
msgstr "続ける"

#: templates/socialaccount/login_cancelled.html:5
#: templates/socialaccount/login_cancelled.html:9
msgid "Login Cancelled"
msgstr "ログインはキャンセルされました"

#: templates/socialaccount/login_cancelled.html:13
#, python-format
msgid ""
"You decided to cancel logging in to our site using one of your existing "
"accounts. If this was a mistake, please proceed to <a "
"href=\"%(login_url)s\">sign in</a>."
msgstr ""
"既存の外部アカウントを使ったログインはキャンセルされました。\n"
"やり直される場合は<a href=\"%(login_url)s\">ログイン</a>ページにお進みくださ"
"い。"

#: templates/socialaccount/messages/account_connected.txt:2
#| msgid "The social account has been connected."
msgid "The third-party account has been connected."
msgstr "外部アカウントがリンクされました"

#: templates/socialaccount/messages/account_connected_other.txt:2
#| msgid "The social account is already connected to a different account."
msgid "The third-party account is already connected to a different account."
msgstr "この外部アカウントは他のアカウントにリンクされています。"

#: templates/socialaccount/messages/account_disconnected.txt:2
msgid "The third-party account has been disconnected."
msgstr "外部アカウントのリンクが解除されました。"

#: templates/socialaccount/signup.html:12
msgid ""
"You are about to use your %(provider_name)s account to login to\n"
"%(site_name)s. As a final step, please complete the following form:"
msgstr ""
"%(provider_name)s アカウントを使って %(site_name)s にログインしようとしています。"
"ユーザー登録のために、以下のフォームに記入してください。"

#: templates/socialaccount/snippets/login.html:9
msgid "Or use a third-party"
msgstr "外部アカウントを使用する"

#: templates/usersessions/messages/sessions_logged_out.txt:2
msgid "Signed out of all other sessions."
msgstr ""

#: templates/usersessions/usersession_list.html:6
#: templates/usersessions/usersession_list.html:10
msgid "Sessions"
msgstr ""

#: templates/usersessions/usersession_list.html:23
msgid "Started At"
msgstr ""

#: templates/usersessions/usersession_list.html:24
#, fuzzy
#| msgid "Email Addresses"
msgid "IP Address"
msgstr "メールアドレス"

#: templates/usersessions/usersession_list.html:25
msgid "Browser"
msgstr ""

#: templates/usersessions/usersession_list.html:27
msgid "Last seen at"
msgstr ""

#: templates/usersessions/usersession_list.html:47
msgid "Current"
msgstr "現在のパスワード"

#: templates/usersessions/usersession_list.html:60
msgid "Sign Out Other Sessions"
msgstr ""

#: usersessions/apps.py:9
msgid "User Sessions"
msgstr ""

#: usersessions/models.py:48
msgid "session key"
msgstr ""

#, fuzzy
#~| msgid "The following email addresses are associated with your account:"
#~ msgid "The following email address is associated with your account:"
#~ msgstr "以下のメールアドレスがアカウントに登録されています："

#, fuzzy
#~| msgid "Confirm Email Address"
#~ msgid "Change Email Address"
#~ msgstr "メールアドレスの確認"

#, python-format
#~ msgid ""
#~ "Please sign in with one\n"
#~ "of your existing third party accounts. Or, <a "
#~ "href=\"%(signup_url)s\">sign up</a>\n"
#~ "for a %(site_name)s account and sign in below:"
#~ msgstr ""
#~ "お持ちの外部アカウントでログインするか、%(site_name)sに <a "
#~ "href=\"%(signup_url)s\">ユーザー登録</a> してログインしてください。"

#~ msgid "or"
#~ msgstr "または"

#~ msgid "change password"
#~ msgstr "パスワード変更"

#~ msgid "OpenID Sign In"
#~ msgstr "OpenID ログイン"

#~ msgid "This email address is already associated with another account."
#~ msgstr "このメールアドレスは別のアカウントで使用されています。"
