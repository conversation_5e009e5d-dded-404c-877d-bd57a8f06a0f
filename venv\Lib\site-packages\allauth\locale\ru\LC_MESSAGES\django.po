# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
msgid ""
msgstr ""
"Project-Id-Version: \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-01-23 12:50-0600\n"
"PO-Revision-Date: 2017-04-05 22:48+0300\n"
"Last-Translator: ANDREI SATSEVICH <<EMAIL>>\n"
"Language-Team: \n"
"Language: ru\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && "
"n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || "
"(n%100>=11 && n%100<=14)? 2 : 3);\n"
"X-Generator: Poedit *******\n"

#: account/adapter.py:50
msgid "Username can not be used. Please use other username."
msgstr "Такое имя пользователя не может быть использовано, выберите другое."

#: account/adapter.py:56
msgid "Too many failed login attempts. Try again later."
msgstr ""
"Слишком много неудачных попыток входа в систему. Повторите попытку позже."

#: account/adapter.py:58
msgid "A user is already registered with this email address."
msgstr "Пользователь с таким адресом электронной почты уже зарегистрирован."

#: account/adapter.py:59
msgid "Please type your current password."
msgstr "Пожалуйста, введите свой текущий пароль."

#: account/adapter.py:60
msgid "Incorrect password."
msgstr "Неверный пароль."

#: account/adapter.py:61
#, python-brace-format
msgid "Password must be a minimum of {0} characters."
msgstr "Минимальное количество символов в пароле: {0}."

#: account/adapter.py:62
msgid "The email address is not assigned to any user account"
msgstr "Адрес электронной почты не закреплен ни за одной учетной записью"

#: account/adapter.py:739
msgid "Use your password"
msgstr "Используйте ваш пароль"

#: account/adapter.py:749
msgid "Use your authenticator app"
msgstr "Используйте приложение-аутентификатор"

#: account/admin.py:23
msgid "Mark selected email addresses as verified"
msgstr "Отметить выбранные адреса электронной почты как подтверждённые"

#: account/apps.py:11
msgid "Accounts"
msgstr "Аккаунты"

#: account/forms.py:60 account/forms.py:449
msgid "You must type the same password each time."
msgstr "Вы должны ввести одинаковый пароль дважды."

#: account/forms.py:92 account/forms.py:412 account/forms.py:554
#: account/forms.py:683
msgid "Password"
msgstr "Пароль"

#: account/forms.py:93
msgid "Remember Me"
msgstr "Запомнить меня"

#: account/forms.py:97
msgid "This account is currently inactive."
msgstr "Учетная запись неактивна."

#: account/forms.py:99
msgid "The email address and/or password you specified are not correct."
msgstr "Указанные вами адрес электронной почты и/или пароль неверны."

#: account/forms.py:102
msgid "The username and/or password you specified are not correct."
msgstr "Имя пользователя и/или пароль не верны."

#: account/forms.py:112 account/forms.py:287 account/forms.py:477
#: account/forms.py:574
msgid "Email address"
msgstr "Адрес электронной почты"

#: account/forms.py:116 account/forms.py:325 account/forms.py:474
#: account/forms.py:569
msgid "Email"
msgstr "E-mail"

#: account/forms.py:119 account/forms.py:122 account/forms.py:277
#: account/forms.py:280
msgid "Username"
msgstr "Имя пользователя"

#: account/forms.py:132
msgid "Username or email"
msgstr "Имя пользователя или e-mail"

#: account/forms.py:135
msgctxt "field label"
msgid "Login"
msgstr "Войти"

#: account/forms.py:146
msgid "Forgot your password?"
msgstr "Забыли свой пароль?"

#: account/forms.py:316
msgid "Email (again)"
msgstr "E-mail (ещё раз)"

#: account/forms.py:320
msgid "Email address confirmation"
msgstr "Подтверждение адреса электронной почты"

#: account/forms.py:328
msgid "Email (optional)"
msgstr "E-mail (опционально)"

#: account/forms.py:383
msgid "You must type the same email each time."
msgstr "Вы должны ввести одинаковый e-mail дважды."

#: account/forms.py:418 account/forms.py:557
msgid "Password (again)"
msgstr "Пароль (ещё раз)"

#: account/forms.py:489
msgid "This email address is already associated with this account."
msgstr "Этот адрес электронной почты уже связан с этой учетной записью."

#: account/forms.py:491
#, python-format
msgid "You cannot add more than %d email addresses."
msgstr "Вы не можете добавить более %d адресов электронной почты."

#: account/forms.py:529
msgid "Current Password"
msgstr "Текущий пароль"

#: account/forms.py:532 account/forms.py:632
msgid "New Password"
msgstr "Новый пароль"

#: account/forms.py:535 account/forms.py:633
msgid "New Password (again)"
msgstr "Новый пароль (ещё раз)"

#: account/forms.py:653
msgid "The password reset token was invalid."
msgstr "Код сброса пароля оказался недействительным."

#: account/models.py:21
msgid "user"
msgstr "пользователь"

#: account/models.py:26 account/models.py:34 account/models.py:138
msgid "email address"
msgstr "адрес электронной почты"

#: account/models.py:28
msgid "verified"
msgstr "подтвержден"

#: account/models.py:29
msgid "primary"
msgstr "основной"

#: account/models.py:35
msgid "email addresses"
msgstr "адреса электронной почты"

#: account/models.py:141
msgid "created"
msgstr "создано"

#: account/models.py:142
msgid "sent"
msgstr "отправлено"

#: account/models.py:143 socialaccount/models.py:63
msgid "key"
msgstr "ключ"

#: account/models.py:148
msgid "email confirmation"
msgstr "подтверждение email адреса"

#: account/models.py:149
msgid "email confirmations"
msgstr "подтверждения email адресов"

#: mfa/adapter.py:20
msgid ""
"You cannot activate two-factor authentication until you have verified your "
"email address."
msgstr ""
"Вы не сможете активировать двухфакторную аутентификацию, пока не пдотвердите "
"свой адрес электронной почты."

#: mfa/adapter.py:23
msgid ""
"You cannot add an email address to an account protected by two-factor "
"authentication."
msgstr ""
"Вы не можете добавить адрес электронной почты в учетную запись, защищенную "
"двухфакторной аутентификацией."

#: mfa/adapter.py:25
msgid "Incorrect code."
msgstr "Неверный код."

#: mfa/adapter.py:27
msgid "You cannot deactivate two-factor authentication."
msgstr "Вы не можете отключить двухфакторную аутентификацию."

#: mfa/apps.py:9
msgid "MFA"
msgstr "Многофакторная аутентификация (MFA)"

#: mfa/forms.py:15 mfa/forms.py:17 mfa/forms.py:52
msgid "Code"
msgstr "Код"

#: mfa/forms.py:50
msgid "Authenticator code"
msgstr "Код аутентификатора"

#: mfa/models.py:19
msgid "Recovery codes"
msgstr "Коды восстановления"

#: mfa/models.py:20
msgid "TOTP Authenticator"
msgstr "Аутентификатор TOTP"

#: socialaccount/adapter.py:31
#, python-format
msgid ""
"An account already exists with this email address. Please sign in to that "
"account first, then connect your %s account."
msgstr ""
"Учетная запись с таким адресом электронной почты уже существует.Пожалуйста, "
"сначала войдите в эту учетную запись, а затем подключите %s."

#: socialaccount/adapter.py:148
msgid "Your account has no password set up."
msgstr "Для вашего аккаунта не установлен пароль."

#: socialaccount/adapter.py:155
msgid "Your account has no verified email address."
msgstr "В вашей учетной записи нет подтвержденного адреса электронной почты."

#: socialaccount/apps.py:9
msgid "Social Accounts"
msgstr "Аккаунты в социальных сетях"

#: socialaccount/models.py:37 socialaccount/models.py:91
msgid "provider"
msgstr "провайдер"

#: socialaccount/models.py:46
msgid "provider ID"
msgstr "провайдер ID"

#: socialaccount/models.py:50
msgid "name"
msgstr "имя"

#: socialaccount/models.py:52
msgid "client id"
msgstr "id клиента"

#: socialaccount/models.py:54
msgid "App ID, or consumer key"
msgstr "ID приложения или ключ потребителя"

#: socialaccount/models.py:57
msgid "secret key"
msgstr "секретный ключ"

#: socialaccount/models.py:60
msgid "API secret, client secret, or consumer secret"
msgstr "Секретный ключ API, клиента или потребителя"

#: socialaccount/models.py:63
msgid "Key"
msgstr "Ключ"

#: socialaccount/models.py:75
msgid "social application"
msgstr "социальное приложение"

#: socialaccount/models.py:76
msgid "social applications"
msgstr "социальные приложения"

#: socialaccount/models.py:111
msgid "uid"
msgstr "UID пользователя"

#: socialaccount/models.py:113
msgid "last login"
msgstr "дата последнего входа в систему"

#: socialaccount/models.py:114
msgid "date joined"
msgstr "дата регистрации"

#: socialaccount/models.py:115
msgid "extra data"
msgstr "дополнительные данные"

#: socialaccount/models.py:119
msgid "social account"
msgstr "аккаунт социальной сети"

#: socialaccount/models.py:120
msgid "social accounts"
msgstr "аккаунты социальных сетей"

#: socialaccount/models.py:154
msgid "token"
msgstr "токен"

#: socialaccount/models.py:155
msgid "\"oauth_token\" (OAuth1) or access token (OAuth2)"
msgstr "\"oauth_token\" (OAuth1) или access token (OAuth2)"

#: socialaccount/models.py:159
msgid "token secret"
msgstr "секретный токен"

#: socialaccount/models.py:160
msgid "\"oauth_token_secret\" (OAuth1) or refresh token (OAuth2)"
msgstr "\"oauth_token_secret\" (OAuth1) или refresh token (OAuth2)"

#: socialaccount/models.py:163
msgid "expires at"
msgstr "истекает"

#: socialaccount/models.py:168
msgid "social application token"
msgstr "токен социального приложения"

#: socialaccount/models.py:169
msgid "social application tokens"
msgstr "токены социальных приложений"

#: socialaccount/providers/douban/views.py:37
msgid "Invalid profile data"
msgstr "Неверные данные профиля"

#: socialaccount/providers/oauth/client.py:85
#, python-format
msgid ""
"Invalid response while obtaining request token from \"%s\". Response was: %s."
msgstr "Неверный ответ во время получения запроса от \"%s\". Ответ был: %s."

#: socialaccount/providers/oauth/client.py:119
#: socialaccount/providers/pocket/client.py:86
#, python-format
msgid "Invalid response while obtaining access token from \"%s\"."
msgstr "Неверный ответ при получении токена доступа от \"%s\"."

#: socialaccount/providers/oauth/client.py:140
#, python-format
msgid "No request token saved for \"%s\"."
msgstr "Нет сохраненного ключа запроса для \"%s\"."

#: socialaccount/providers/oauth/client.py:191
#, python-format
msgid "No access token saved for \"%s\"."
msgstr "Нет сохраненного ключа доступа для \"%s\"."

#: socialaccount/providers/oauth/client.py:212
#, python-format
msgid "No access to private resources at \"%s\"."
msgstr "Доступ к ресурсам закрыт \"%s\"."

#: socialaccount/providers/pocket/client.py:41
#, python-format
msgid "Invalid response while obtaining request token from \"%s\"."
msgstr "Неверный ответ во время получения запроса от \"%s\"."

#: templates/account/account_inactive.html:5
#: templates/account/account_inactive.html:9
msgid "Account Inactive"
msgstr "Аккаунт неактивен"

#: templates/account/account_inactive.html:11
msgid "This account is inactive."
msgstr "Этот аккаунт неактивен."

#: templates/account/base_reauthenticate.html:5
#: templates/account/base_reauthenticate.html:9
msgid "Confirm Access"
msgstr "Подтвердите доступ"

#: templates/account/base_reauthenticate.html:11
msgid "Please reauthenticate to safeguard your account."
msgstr ""
"Пожалуйста, пройдите повторную аутентификацию, чтобы обезопасить свою "
"учетную запись."

#: templates/account/base_reauthenticate.html:17
msgid "Alternative options"
msgstr "Альтернативные варианты"

#: templates/account/email.html:4 templates/account/email.html:8
msgid "Email Addresses"
msgstr "Адреса электронной почты"

#: templates/account/email.html:11
msgid "The following email addresses are associated with your account:"
msgstr "С вашей учетной записью связаны следующие адреса электронной почты:"

#: templates/account/email.html:23
msgid "Verified"
msgstr "Подтвержден"

#: templates/account/email.html:27
msgid "Unverified"
msgstr "Не подтвержден"

#: templates/account/email.html:32
msgid "Primary"
msgstr "Основной"

#: templates/account/email.html:42
msgid "Make Primary"
msgstr "Сделать основным"

#: templates/account/email.html:45 templates/account/email_change.html:37
msgid "Re-send Verification"
msgstr "Отправить подтверждение ещё раз"

#: templates/account/email.html:48 templates/socialaccount/connections.html:40
msgid "Remove"
msgstr "Удалить"

#: templates/account/email.html:57
msgid "Add Email Address"
msgstr "Добавить адрес электронной почты"

#: templates/account/email.html:70
msgid "Add Email"
msgstr "Добавить E-mail"

#: templates/account/email.html:79
msgid "Do you really want to remove the selected email address?"
msgstr "Вы действительно хотите удалить выбранный адрес электронной почты?"

#: templates/account/email/account_already_exists_message.txt:4
#, python-format
msgid ""
"You are receiving this email because you or someone else tried to signup for "
"an\n"
"account using email address:\n"
"\n"
"%(email)s\n"
"\n"
"However, an account using that email address already exists.  In case you "
"have\n"
"forgotten about this, please use the password forgotten procedure to "
"recover\n"
"your account:\n"
"\n"
"%(password_reset_url)s"
msgstr ""
"Вы получили это письмо, потому что вы или кто-то другой пытались "
"зарегистрировать учетную запись, используя адрес электронной почты: \n"
"%(email)s\n"
"\n"
"Однако учетная запись с таким адресом электронной почты уже существует. В "
"случае, если вы вы забыли об этом, воспользуйтесь процедурой \"Забыли "
"пароль\", чтобы восстановить свою учетную запись: \n"
"%(password_reset_url)s"

#: templates/account/email/account_already_exists_subject.txt:3
msgid "Account Already Exists"
msgstr "Учетная запись уже существует"

#: templates/account/email/base_message.txt:1
#, python-format
msgid "Hello from %(site_name)s!"
msgstr "Вас приветствует %(site_name)s!"

#: templates/account/email/base_message.txt:5
#, python-format
msgid ""
"Thank you for using %(site_name)s!\n"
"%(site_domain)s"
msgstr ""
"Благодарим вас за использование сайта «%(site_name)s!»\n"
"%(site_domain)s"

#: templates/account/email/base_notification.txt:5
msgid ""
"You are receiving this mail because the following change was made to your "
"account:"
msgstr ""

#: templates/account/email/base_notification.txt:10
#, python-format
msgid ""
"If you do not recognize this change then please take proper security "
"precautions immediately. The change to your account originates from:\n"
"\n"
"- IP address: %(ip)s\n"
"- Browser: %(user_agent)s\n"
"- Date: %(timestamp)s"
msgstr ""

#: templates/account/email/email_changed_message.txt:4
#, python-format
msgid "Your email has been changed from %(from_email)s to %(to_email)s."
msgstr ""

#: templates/account/email/email_changed_subject.txt:3
#, fuzzy
#| msgid "Cancel Change"
msgid "Email Changed"
msgstr "Отменить изменения"

#: templates/account/email/email_confirm_message.txt:4
#, fuzzy
#| msgid "Your password has been deleted."
msgid "Your email has been confirmed."
msgstr "Ваш пароль был удален."

#: templates/account/email/email_confirm_subject.txt:3
#, fuzzy
#| msgid "email confirmation"
msgid "Email Confirmation"
msgstr "подтверждение email адреса"

#: templates/account/email/email_confirmation_message.txt:5
#, python-format
msgid ""
"You're receiving this email because user %(user_display)s has given your "
"email address to register an account on %(site_domain)s.\n"
"\n"
"To confirm this is correct, go to %(activate_url)s"
msgstr ""
"Вы получили это письмо, потому что пользователь %(user_display)s указал ваш "
"адрес электронной почты для регистрации учетной записи на сайте "
"%(site_domain)s.\n"
"\n"
"Чтобы подтвердить, перейдите по ссылке %(activate_url)s"

#: templates/account/email/email_confirmation_subject.txt:3
msgid "Please Confirm Your Email Address"
msgstr "Пожалуйста, подтвердите Ваш адрес электронной почты"

#: templates/account/email/email_deleted_message.txt:4
#, python-format
msgid "Email address %(deleted_email)s has been removed from your account."
msgstr ""

#: templates/account/email/email_deleted_subject.txt:3
#, fuzzy
#| msgid "Remove"
msgid "Email Removed"
msgstr "Удалить"

#: templates/account/email/password_changed_message.txt:4
#, fuzzy
#| msgid "Your password is now changed."
msgid "Your password has been changed."
msgstr "Ваш пароль изменён."

#: templates/account/email/password_changed_subject.txt:3
#, fuzzy
#| msgid "Password (again)"
msgid "Password Changed"
msgstr "Пароль (ещё раз)"

#: templates/account/email/password_reset_key_message.txt:4
msgid ""
"You're receiving this email because you or someone else has requested a "
"password reset for your user account.\n"
"It can be safely ignored if you did not request a password reset. Click the "
"link below to reset your password."
msgstr ""
"Вы получили это письмо, потому что вы или кто-то другой запросили сброс "
"пароля для вашей учетной записи.\n"
"Если это были не вы, просто проигнорируйте это письмо. Нажмите на ссылку "
"ниже, чтобы сбросить пароль."

#: templates/account/email/password_reset_key_message.txt:9
#, python-format
msgid "In case you forgot, your username is %(username)s."
msgstr "Если вы вдруг забыли, ваше имя пользователя: %(username)s."

#: templates/account/email/password_reset_key_subject.txt:3
#: templates/account/email/unknown_account_subject.txt:3
msgid "Password Reset Email"
msgstr "Письмо для сброса пароля"

#: templates/account/email/password_reset_message.txt:4
#, fuzzy
#| msgid "Your password has been deleted."
msgid "Your password has been reset."
msgstr "Ваш пароль был удален."

#: templates/account/email/password_reset_subject.txt:3
#: templates/account/password_reset.html:4
#: templates/account/password_reset.html:8
#: templates/account/password_reset_done.html:6
#: templates/account/password_reset_done.html:10
msgid "Password Reset"
msgstr "Сброс пароля"

#: templates/account/email/password_set_message.txt:4
#, fuzzy
#| msgid "Your password has been deleted."
msgid "Your password has been set."
msgstr "Ваш пароль был удален."

#: templates/account/email/password_set_subject.txt:3
#, fuzzy
#| msgid "Password Reset"
msgid "Password Set"
msgstr "Сброс пароля"

#: templates/account/email/unknown_account_message.txt:4
#, python-format
msgid ""
"You are receiving this email because you or someone else has requested a\n"
"password for your user account. However, we do not have any record of a "
"user\n"
"with email %(email)s in our database.\n"
"\n"
"This mail can be safely ignored if you did not request a password reset.\n"
"\n"
"If it was you, you can sign up for an account using the link below."
msgstr ""
"Вы получили это письмо, потому что вы или кто-то другой запросили сброс "
"пароля для вашей учетной записи.Однако нашей базе данных ничего не известно "
"о пользователес электронной почтой %(email)s.\n"
"\n"
"Если это были не вы, просто проигнорируйте это письмо.\n"
"Если же это всё-таки были вы, вы можете зарегистрировать аккаунт по ссылке "
"ниже."

#: templates/account/email_change.html:5 templates/account/email_change.html:9
msgid "Email Address"
msgstr "Адрес электронной почты"

#: templates/account/email_change.html:21
#: templates/account/email_change.html:29
msgid "Current email"
msgstr "Текущий e-mail"

#: templates/account/email_change.html:31
msgid "Changing to"
msgstr "Изменить на"

#: templates/account/email_change.html:35
msgid "Your email address is still pending verification."
msgstr "Ваш адрес электронной почты ожидает подтверждения."

#: templates/account/email_change.html:41
msgid "Cancel Change"
msgstr "Отменить изменения"

#: templates/account/email_change.html:49
msgid "Change to"
msgstr "Изменить на"

#: templates/account/email_change.html:55
#: templates/allauth/layouts/base.html:29
msgid "Change Email"
msgstr "Изменить E-mail"

#: templates/account/email_confirm.html:6
#: templates/account/email_confirm.html:10
msgid "Confirm Email Address"
msgstr "Подтвердите адрес электронной почты"

#: templates/account/email_confirm.html:16
#, python-format
msgid ""
"Please confirm that <a href=\"mailto:%(email)s\">%(email)s</a> is an email "
"address for user %(user_display)s."
msgstr ""
"Пожалуйста, подтвердите <a href=\"mailto:%(email)s\">%(email)s</a> для "
"пользователя %(user_display)s."

#: templates/account/email_confirm.html:23
#: templates/account/reauthenticate.html:20
#: templates/mfa/reauthenticate.html:20
msgid "Confirm"
msgstr "Подтвердить"

#: templates/account/email_confirm.html:29
#: templates/account/messages/email_confirmation_failed.txt:2
#, python-format
msgid ""
"Unable to confirm %(email)s because it is already confirmed by a different "
"account."
msgstr ""
"Невозможно подтвердить %(email)s, потому что он уже прикреплен к другой "
"учетной записи."

#: templates/account/email_confirm.html:35
#, python-format
msgid ""
"This email confirmation link expired or is invalid. Please <a "
"href=\"%(email_url)s\">issue a new email confirmation request</a>."
msgstr ""
"Ссылка некорректна или срок её действия истек. Пожалуйста, <a "
"href=\"%(email_url)s\">отправьте новый запрос на подтверждение электронной "
"почты</a>."

#: templates/account/login.html:5 templates/account/login.html:9
#: templates/account/login.html:29 templates/allauth/layouts/base.html:36
#: templates/mfa/authenticate.html:5 templates/mfa/authenticate.html:23
#: templates/openid/login.html:5 templates/openid/login.html:9
#: templates/openid/login.html:20 templates/socialaccount/login.html:5
msgid "Sign In"
msgstr "Войти"

#: templates/account/login.html:12
#, python-format
msgid ""
"If you have not created an account yet, then please\n"
"    <a href=\"%(signup_url)s\">sign up</a> first."
msgstr ""
"Если у вас ещё нет учётной записи, пожалуйста, сначала <a "
"href=\"%(signup_url)s\">зарегистрируйтесь</a>."

#: templates/account/logout.html:4 templates/account/logout.html:8
#: templates/account/logout.html:23 templates/allauth/layouts/base.html:32
#: templates/usersessions/usersession_list.html:64
msgid "Sign Out"
msgstr "Выйти"

#: templates/account/logout.html:10
msgid "Are you sure you want to sign out?"
msgstr "Вы уверены, что хотите выйти?"

#: templates/account/messages/cannot_delete_primary_email.txt:2
#, python-format
msgid "You cannot remove your primary email address (%(email)s)."
msgstr ""
"Вы не можете удалить свой основной адрес электронной почты (%(email)s)."

#: templates/account/messages/email_confirmation_sent.txt:2
#, python-format
msgid "Confirmation email sent to %(email)s."
msgstr "Подтверждающее письмо отправлено на %(email)s."

#: templates/account/messages/email_confirmed.txt:2
#, python-format
msgid "You have confirmed %(email)s."
msgstr "Адрес %(email)s подтверждён."

#: templates/account/messages/email_deleted.txt:2
#, python-format
msgid "Removed email address %(email)s."
msgstr "Удален адрес электронной почты %(email)s."

#: templates/account/messages/logged_in.txt:4
#, python-format
msgid "Successfully signed in as %(name)s."
msgstr "Успешный вход под именем %(name)s."

#: templates/account/messages/logged_out.txt:2
msgid "You have signed out."
msgstr "Вы вышли."

#: templates/account/messages/password_changed.txt:2
msgid "Password successfully changed."
msgstr "Пароль успешно изменён."

#: templates/account/messages/password_set.txt:2
msgid "Password successfully set."
msgstr "Пароль успешно установлен."

#: templates/account/messages/primary_email_set.txt:2
msgid "Primary email address set."
msgstr "Установлен основной адрес электронной почты"

#: templates/account/messages/unverified_primary_email.txt:2
msgid "Your primary email address must be verified."
msgstr "Ваш основной адрес электронной почты должен быть подтвержден."

#: templates/account/password_change.html:4
#: templates/account/password_change.html:8
#: templates/account/password_change.html:19
#: templates/account/password_reset_from_key.html:5
#: templates/account/password_reset_from_key.html:12
#: templates/account/password_reset_from_key.html:29
#: templates/account/password_reset_from_key_done.html:5
#: templates/account/password_reset_from_key_done.html:9
msgid "Change Password"
msgstr "Изменить пароль"

#: templates/account/password_change.html:21
msgid "Forgot Password?"
msgstr "Забыли пароль?"

#: templates/account/password_reset.html:14
msgid ""
"Forgotten your password? Enter your email address below, and we'll send you "
"an email allowing you to reset it."
msgstr ""
"Забыли свой пароль? Введите Ваш адрес электронной почты ниже, и мы отправим "
"Вам письмо, чтобы вы могли его сбросить."

#: templates/account/password_reset.html:25
msgid "Reset My Password"
msgstr "Сбросить мой пароль"

#: templates/account/password_reset.html:29
msgid "Please contact us if you have any trouble resetting your password."
msgstr "Свяжитесь с нами, если у вас возникли сложности со сменой пароля."

#: templates/account/password_reset_done.html:16
msgid ""
"We have sent you an email. If you have not received it please check your "
"spam folder. Otherwise contact us if you do not receive it in a few minutes."
msgstr ""
"Мы отправили вам письмо. Если вы его не получили, проверьте папку \"Спам\". "
"Свяжитесь с нами, если вы не получили письмо в течение нескольких минут."

#: templates/account/password_reset_from_key.html:10
msgid "Bad Token"
msgstr "Неправильный ключ"

#: templates/account/password_reset_from_key.html:18
#, python-format
msgid ""
"The password reset link was invalid, possibly because it has already been "
"used.  Please request a <a href=\"%(passwd_reset_url)s\">new password reset</"
"a>."
msgstr ""
"Ссылка на сброс пароля неверна, вероятно, она уже была использована. Для "
"нового сброса пароля <a href=\"%(passwd_reset_url)s\">перейдите по ссылке</"
"a>."

#: templates/account/password_reset_from_key_done.html:11
msgid "Your password is now changed."
msgstr "Ваш пароль изменён."

#: templates/account/password_set.html:5 templates/account/password_set.html:9
#: templates/account/password_set.html:20
msgid "Set Password"
msgstr "Установить пароль"

#: templates/account/reauthenticate.html:5
msgid "Enter your password:"
msgstr "Введите пароль:"

#: templates/account/signup.html:4 templates/socialaccount/signup.html:5
msgid "Signup"
msgstr "Регистрация"

#: templates/account/signup.html:8 templates/account/signup.html:27
#: templates/allauth/layouts/base.html:39 templates/socialaccount/signup.html:9
#: templates/socialaccount/signup.html:29
msgid "Sign Up"
msgstr "Регистрация"

#: templates/account/signup.html:11
#, python-format
msgid ""
"Already have an account? Then please <a href=\"%(login_url)s\">sign in</a>."
msgstr "Уже зарегистрированы? <a href=\"%(login_url)s\">Войдите</a>."

#: templates/account/signup_closed.html:5
#: templates/account/signup_closed.html:9
msgid "Sign Up Closed"
msgstr "Регистрация закрыта"

#: templates/account/signup_closed.html:11
msgid "We are sorry, but the sign up is currently closed."
msgstr "Мы сожалеем, но в текущий момент регистрация закрыта."

#: templates/account/snippets/already_logged_in.html:7
msgid "Note"
msgstr "Заметка"

#: templates/account/snippets/already_logged_in.html:7
#, python-format
msgid "You are already logged in as %(user_display)s."
msgstr "Вы уже вошли как %(user_display)s."

#: templates/account/snippets/warn_no_email.html:3
msgid "Warning:"
msgstr "Внимание:"

#: templates/account/snippets/warn_no_email.html:3
msgid ""
"You currently do not have any email address set up. You should really add an "
"email address so you can receive notifications, reset your password, etc."
msgstr ""
"Сейчас у вас нет прикрепленного адрес электронной почты. Рекомендуем "
"добавить, чтобы иметь возможность получать уведомления, сбрасывать пароль и "
"и т.д."

#: templates/account/verification_sent.html:5
#: templates/account/verification_sent.html:9
#: templates/account/verified_email_required.html:5
#: templates/account/verified_email_required.html:9
msgid "Verify Your Email Address"
msgstr "Подтвердите Ваш адрес электронной почты"

#: templates/account/verification_sent.html:12
msgid ""
"We have sent an email to you for verification. Follow the link provided to "
"finalize the signup process. If you do not see the verification email in "
"your main inbox, check your spam folder. Please contact us if you do not "
"receive the verification email within a few minutes."
msgstr ""
"Мы отправили вам письмо с подтверждением. Перейдите по указанной ссылке, "
"чтобы завершить процесс регистрации. Если вы не видите письмо с "
"подтверждением в вашем основном почтовом ящике, проверьте папку \"Спам\"."
"Пожалуйста, свяжитесь с нами, если вы не получите письмо с подтверждением в "
"течение нескольких минут."

#: templates/account/verified_email_required.html:13
msgid ""
"This part of the site requires us to verify that\n"
"you are who you claim to be. For this purpose, we require that you\n"
"verify ownership of your email address. "
msgstr ""
"Эта часть сайта требует от нас подтверждения того, что вы являетесь тем, за "
"кого себя выдаете. Для этого нам необходимо подтвердить собственность вашего "
"адреса электронной почты."

#: templates/account/verified_email_required.html:18
msgid ""
"We have sent an email to you for\n"
"verification. Please click on the link inside that email. If you do not see "
"the verification email in your main inbox, check your spam folder. "
"Otherwise\n"
"contact us if you do not receive it within a few minutes."
msgstr ""
"Мы отправили вам письмо\n"
"для проверки. Пожалуйста, перейдите по ссылке. Если вы не видите письмо с "
"подтверждением в вашем основном почтовом ящике, проверьте папку \"Спам\".В "
"противном случае свяжитесь с нами, если вы не получите его в течение "
"нескольких минут."

#: templates/account/verified_email_required.html:23
#, python-format
msgid ""
"<strong>Note:</strong> you can still <a href=\"%(email_url)s\">change your "
"email address</a>."
msgstr ""
"<strong>Заметка:</strong> вы можете <a href=\"%(email_url)s\">изменить свой "
"адрес электронной почты</a>."

#: templates/allauth/layouts/base.html:18
msgid "Messages:"
msgstr "Сообщения:"

#: templates/allauth/layouts/base.html:25
msgid "Menu:"
msgstr "Меню:"

#: templates/mfa/authenticate.html:9 templates/mfa/index.html:5
#: templates/mfa/index.html:9
msgid "Two-Factor Authentication"
msgstr "Двухфакторная аутентификация"

#: templates/mfa/authenticate.html:12
msgid ""
"Your account is protected by two-factor authentication. Please enter an "
"authenticator code:"
msgstr ""
"Ваша учетная запись защищена двухфакторной аутентификацией. Пожалуйста, "
"введите код аутентификатора:"

#: templates/mfa/authenticate.html:27
msgid "Cancel"
msgstr "Отмена"

#: templates/mfa/email/recovery_codes_generated_message.txt:4
#, fuzzy
#| msgid "A new set of recovery codes has been generated."
msgid ""
"A new set of Two-Factor Authentication recovery codes has been generated."
msgstr "Был создан новый набор кодов восстановления."

#: templates/mfa/email/recovery_codes_generated_subject.txt:3
#, fuzzy
#| msgid "Recovery Codes"
msgid "New Recovery Codes Generated"
msgstr "Коды восстановления"

#: templates/mfa/email/totp_activated_message.txt:4
#: templates/mfa/messages/totp_activated.txt:2
msgid "Authenticator app activated."
msgstr "Активировано приложение Аутентификатор."

#: templates/mfa/email/totp_activated_subject.txt:3
#, fuzzy
#| msgid "Authenticator app activated."
msgid "Authenticator App Activated"
msgstr "Активировано приложение Аутентификатор."

#: templates/mfa/email/totp_deactivated_message.txt:4
#: templates/mfa/messages/totp_deactivated.txt:2
msgid "Authenticator app deactivated."
msgstr "Приложение Аутентификатор деактивировано."

#: templates/mfa/email/totp_deactivated_subject.txt:3
#, fuzzy
#| msgid "Authenticator app deactivated."
msgid "Authenticator App Deactivated"
msgstr "Приложение Аутентификатор деактивировано."

#: templates/mfa/index.html:14 templates/mfa/totp/base.html:4
msgid "Authenticator App"
msgstr "Приложение аутентификатор"

#: templates/mfa/index.html:18
msgid "Authentication using an authenticator app is active."
msgstr "Аутентификация с помощью приложения-аутентификатора активна."

#: templates/mfa/index.html:20
msgid "An authenticator app is not active."
msgstr "Приложение аутентификатора неактивно."

#: templates/mfa/index.html:28 templates/mfa/totp/deactivate_form.html:24
msgid "Deactivate"
msgstr "Деактивировать"

#: templates/mfa/index.html:32 templates/mfa/totp/activate_form.html:32
msgid "Activate"
msgstr "Активировать"

#: templates/mfa/index.html:42 templates/mfa/recovery_codes/base.html:4
#: templates/mfa/recovery_codes/generate.html:6
#: templates/mfa/recovery_codes/index.html:6
msgid "Recovery Codes"
msgstr "Коды восстановления"

#: templates/mfa/index.html:47 templates/mfa/recovery_codes/index.html:9
#, python-format
msgid ""
"There is %(unused_count)s out of %(total_count)s recovery codes available."
msgid_plural ""
"There are %(unused_count)s out of %(total_count)s recovery codes available."
msgstr[0] "Доступно %(unused_count)s из %(total_count)s кодов восстановления."
msgstr[1] "Доступнен %(unused_count)s из %(total_count)s кодов восстановления."
msgstr[2] "Доступны %(unused_count)s из %(total_count)s кодов восстановления."
msgstr[3] "Доступны %(unused_count)s из %(total_count)s кодов восстановления."

#: templates/mfa/index.html:50
msgid "No recovery codes set up."
msgstr "Коды восстановления не установлены."

#: templates/mfa/index.html:59
msgid "View"
msgstr "Посмотреть"

#: templates/mfa/index.html:65
msgid "Download"
msgstr "Скачать"

#: templates/mfa/index.html:73 templates/mfa/recovery_codes/generate.html:29
msgid "Generate"
msgstr "Генерировать"

#: templates/mfa/messages/recovery_codes_generated.txt:2
msgid "A new set of recovery codes has been generated."
msgstr "Был создан новый набор кодов восстановления."

#: templates/mfa/reauthenticate.html:5
msgid "Enter an authenticator code:"
msgstr "Введите код аутентификатора:"

#: templates/mfa/recovery_codes/generate.html:9
msgid "You are about to generate a new set of recovery codes for your account."
msgstr ""
"Вы собираетесь сгенерировать новый набор кодов восстановления для вашей "
"учетной записи."

#: templates/mfa/recovery_codes/generate.html:11
msgid "This action will invalidate your existing codes."
msgstr "Это действие аннулирует существующие коды."

#: templates/mfa/recovery_codes/generate.html:13
msgid "Are you sure?"
msgstr "Вы уверены?"

#: templates/mfa/recovery_codes/index.html:13
msgid "Unused codes"
msgstr "Неиспользуемые коды"

#: templates/mfa/recovery_codes/index.html:25
msgid "Download codes"
msgstr "Скачать коды"

#: templates/mfa/recovery_codes/index.html:30
msgid "Generate new codes"
msgstr "Генерировать новые коды"

#: templates/mfa/totp/activate_form.html:4
#: templates/mfa/totp/activate_form.html:8
msgid "Activate Authenticator App"
msgstr "Активировать приложение Аутентификатор"

#: templates/mfa/totp/activate_form.html:11
msgid ""
"To protect your account with two-factor authentication, scan the QR code "
"below with your authenticator app. Then, input the verification code "
"generated by the app below."
msgstr ""
"Чтобы защитить свою учетную запись с помощью двухфакторной аутентификации, "
"отсканируйте приведенный ниже QR-код с помощью приложения-аутентификатора. "
"Затем введите проверочный код, сгенерированный приложением ниже."

#: templates/mfa/totp/activate_form.html:21
msgid "Authenticator secret"
msgstr "Код аутентификатора"

#: templates/mfa/totp/activate_form.html:24
msgid ""
"You can store this secret and use it to reinstall your authenticator app at "
"a later time."
msgstr ""
"Вы можете сохранить этот секрет ключ и использовать его для повторной "
"установки приложения аутентификатора в будущем."

#: templates/mfa/totp/deactivate_form.html:5
#: templates/mfa/totp/deactivate_form.html:9
msgid "Deactivate Authenticator App"
msgstr "Отключите приложение Аутентификатор"

#: templates/mfa/totp/deactivate_form.html:12
msgid ""
"You are about to deactivate authenticator app based authentication. Are you "
"sure?"
msgstr ""
"Вы собираетесь отключить аутентификацию с использованием приложения "
"Аутентификатор. Вы уверены?"

#: templates/socialaccount/authentication_error.html:5
#: templates/socialaccount/authentication_error.html:9
#, fuzzy
#| msgid "Social Network Login Failure"
msgid "Third-Party Login Failure"
msgstr "Ошибка авторизации через социальную сеть"

#: templates/socialaccount/authentication_error.html:11
#, fuzzy
#| msgid ""
#| "An error occurred while attempting to login via your social network "
#| "account."
msgid ""
"An error occurred while attempting to login via your third-party account."
msgstr "Произошла ошибка во время авторизации через социальную сеть."

#: templates/socialaccount/connections.html:5
#: templates/socialaccount/connections.html:9
msgid "Account Connections"
msgstr "Прикрепленные аккаунты"

#: templates/socialaccount/connections.html:13
msgid ""
"You can sign in to your account using any of the following third-party "
"accounts:"
msgstr "Вы можете авторизоваться, используя следующие сервисы:"

#: templates/socialaccount/connections.html:45
#, fuzzy
#| msgid ""
#| "You currently have no social network accounts connected to this account."
msgid "You currently have no third-party accounts connected to this account."
msgstr "Нет прикрепленных аккаунтов социальных сетей."

#: templates/socialaccount/connections.html:48
msgid "Add a Third-Party Account"
msgstr "Добавить внешний аккаунт"

#: templates/socialaccount/email/account_connected_message.txt:4
#, python-format
msgid ""
"A third-party account from %(provider)s has been connected to your account."
msgstr ""

#: templates/socialaccount/email/account_connected_subject.txt:3
#, fuzzy
#| msgid "Add a Third-Party Account"
msgid "Third-Party Account Connected"
msgstr "Добавить внешний аккаунт"

#: templates/socialaccount/email/account_disconnected_message.txt:4
#, python-format
msgid ""
"A third-party account from %(provider)s has been disconnected from your "
"account."
msgstr ""

#: templates/socialaccount/email/account_disconnected_subject.txt:3
#, fuzzy
#| msgid "Add a Third-Party Account"
msgid "Third-Party Account Disconnected"
msgstr "Добавить внешний аккаунт"

#: templates/socialaccount/login.html:10
#, python-format
msgid "Connect %(provider)s"
msgstr "Соединение с %(provider)s"

#: templates/socialaccount/login.html:13
#, python-format
msgid "You are about to connect a new third-party account from %(provider)s."
msgstr "Вы собираетесь подключить новый сторонний аккаунт из %(provider)s"

#: templates/socialaccount/login.html:17
#, python-format
msgid "Sign In Via %(provider)s"
msgstr "Вход через %(provider)s"

#: templates/socialaccount/login.html:20
#, python-format
msgid "You are about to sign in using a third-party account from %(provider)s."
msgstr ""
"Вы собираетесь войти, используя стороннюю учетную запись из %(provider)s"

#: templates/socialaccount/login.html:27
msgid "Continue"
msgstr "Продолжить"

#: templates/socialaccount/login_cancelled.html:5
#: templates/socialaccount/login_cancelled.html:9
msgid "Login Cancelled"
msgstr "Авторизация отменена"

#: templates/socialaccount/login_cancelled.html:13
#, python-format
msgid ""
"You decided to cancel logging in to our site using one of your existing "
"accounts. If this was a mistake, please proceed to <a "
"href=\"%(login_url)s\">sign in</a>."
msgstr ""
"Вы прервали авторизацию, используя один из ваших аккаутов. Если это было "
"ошибкой, перейдите к <a href=\"%(login_url)s\">авторизации</a>. "

#: templates/socialaccount/messages/account_connected.txt:2
#, fuzzy
#| msgid "The social account has been connected."
msgid "The third-party account has been connected."
msgstr "Аккаунт социальной сети был прикреплён."

#: templates/socialaccount/messages/account_connected_other.txt:2
#, fuzzy
#| msgid "The social account is already connected to a different account."
msgid "The third-party account is already connected to a different account."
msgstr "Аккаунт социальной сети уже прикреплен к другому пользователю."

#: templates/socialaccount/messages/account_disconnected.txt:2
#, fuzzy
#| msgid "The social account has been disconnected."
msgid "The third-party account has been disconnected."
msgstr "Акакаунт социальной сети был откреплен."

#: templates/socialaccount/signup.html:12
#, python-format
msgid ""
"You are about to use your %(provider_name)s account to login to\n"
"%(site_name)s. As a final step, please complete the following form:"
msgstr ""
"Вы используете %(provider_name)s для авторизации на \n"
"%(site_name)s. Чтобы завершить, заполните следующую форму:"

#: templates/socialaccount/snippets/login.html:9
msgid "Or use a third-party"
msgstr "Или использовать сторонний"

#: templates/usersessions/messages/sessions_logged_out.txt:2
msgid "Signed out of all other sessions."
msgstr "Выйти из всех остальных сеансов."

#: templates/usersessions/usersession_list.html:6
#: templates/usersessions/usersession_list.html:10
msgid "Sessions"
msgstr "Сеансы"

#: templates/usersessions/usersession_list.html:23
msgid "Started At"
msgstr "Начато в"

#: templates/usersessions/usersession_list.html:24
msgid "IP Address"
msgstr "IP-адрес"

#: templates/usersessions/usersession_list.html:25
msgid "Browser"
msgstr "Браузер"

#: templates/usersessions/usersession_list.html:27
msgid "Last seen at"
msgstr "Последний вход в"

#: templates/usersessions/usersession_list.html:47
msgid "Current"
msgstr "Текущий"

#: templates/usersessions/usersession_list.html:60
msgid "Sign Out Other Sessions"
msgstr "Выйти из других сеансов"

#: usersessions/apps.py:9
msgid "User Sessions"
msgstr "Сеансы пользователей"

#: usersessions/models.py:48
msgid "session key"
msgstr "ключ сеанса"

#, fuzzy
#~| msgid "The following email addresses are associated with your account:"
#~ msgid "The following email address is associated with your account:"
#~ msgstr "Следующие e-mail адреса прикреплены к вашему аккаунту:"

#, fuzzy
#~| msgid "Confirm Email Address"
#~ msgid "Change Email Address"
#~ msgstr "Подтвердите e-mail адрес."

#, python-format
#~ msgid ""
#~ "Please sign in with one\n"
#~ "of your existing third party accounts. Or, <a "
#~ "href=\"%(signup_url)s\">sign up</a>\n"
#~ "for a %(site_name)s account and sign in below:"
#~ msgstr ""
#~ "Пожалуйста, войдите с одним\n"
#~ "из ваших внешних аккаунтов. Или <a "
#~ "href=\"%(signup_url)s\">зарегистрируйтесь</a>\n"
#~ "и авторизуйтесь на сайте %(site_name)s:"

#~ msgid "or"
#~ msgstr "или"

#~ msgid "change password"
#~ msgstr "изменить пароль"

#~ msgid "OpenID Sign In"
#~ msgstr "Войти с OpenID"

#~ msgid "This email address is already associated with another account."
#~ msgstr "Указанный e-mail прикреплен к другому пользователю."

#~ msgid ""
#~ "We have sent you an e-mail. Please contact us if you do not receive it "
#~ "within a few minutes."
#~ msgstr ""
#~ "Мы отправили вам письмо. Пожалуйста, свяжитесь с нами, если не получили "
#~ "его в течение нескольких минут."

#~ msgid "Account"
#~ msgstr "Аккаунт"

#~ msgid "The login and/or password you specified are not correct."
#~ msgstr "Логин и/или пароль не верны."

#~ msgid "Usernames can only contain letters, digits and @/./+/-/_."
#~ msgstr "Имя пользователя может включать буквы, цифры и @/./+/-/_."

#~ msgid "This username is already taken. Please choose another."
#~ msgstr ""
#~ "Такое имя пользователя уже используется на сайте. Пожалуйста выберите "
#~ "другое."

#, fuzzy
#~| msgid "Sign In"
#~ msgid "Shopify Sign In"
#~ msgstr "Войти"

#~ msgid ""
#~ "You have confirmed that <a href=\"mailto:%(email)s\">%(email)s</a> is an "
#~ "e-mail address for user %(user_display)s."
#~ msgstr ""
#~ "Вы подтвердили адрес <a href=\"mailto:%(email)s\">%(email)s</a> для "
#~ "пользователя %(user_display)s."

#~ msgid "Thanks for using our site!"
#~ msgstr "Спасибо за использование нашего сайта!"

#~ msgid "Confirmation email sent to %(email)s"
#~ msgstr "Подтверждение выслано на %(email)s"

#~ msgid "Delete Password"
#~ msgstr "Удалить пароль"

#~ msgid ""
#~ "You may delete your password since you are currently logged in using "
#~ "OpenID."
#~ msgstr "Вы можете удалить свой пароль, при использовании OpenID."

#~ msgid "delete my password"
#~ msgstr "удалите мой пароль"

#~ msgid "Password Deleted"
#~ msgstr "Пароль удалён"
