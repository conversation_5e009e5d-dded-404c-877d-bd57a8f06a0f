<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Search Results</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-50">
    <div class="max-w-4xl mx-auto px-4 py-8">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-2xl font-bold">Debug Search Results</h1>
            <div class="space-x-2">
                <a href="/search-by-image/" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                    Refresh
                </a>
                <button onclick="clearSession()" class="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600">
                    Clear Session
                </button>
            </div>
        </div>
        
        <!-- Debug Information -->
        <div class="bg-white rounded-lg shadow p-6 mb-6">
            <h2 class="text-lg font-semibold mb-4">Debug Information</h2>
            <div class="grid grid-cols-2 gap-4 text-sm">
                <div>
                    <strong>Type:</strong> {{ type|default:"None" }}
                </div>
                <div>
                    <strong>Image URL:</strong> {{ image_url|default:"None" }}
                </div>
                <div>
                    <strong>Predicted Class:</strong> {{ predicted_class|default:"None" }}
                </div>
                <div>
                    <strong>Category Name:</strong> {{ category_name|default:"None" }}
                </div>
                <div>
                    <strong>Error Message:</strong> {{ error_message|default:"None" }}
                </div>
                <div>
                    <strong>Has Features:</strong> {{ has_features|default:"None" }}
                </div>
                <div>
                    <strong>Products Count:</strong> {{ products|length|default:"0" }}
                </div>
                <div>
                    <strong>Categories Count:</strong> {{ categories|length|default:"0" }}
                </div>
            </div>
        </div>

        <!-- Image Display -->
        {% if image_url %}
        <div class="bg-white rounded-lg shadow p-6 mb-6">
            <h2 class="text-lg font-semibold mb-4">Uploaded Image</h2>
            <img src="{{ image_url }}" alt="Uploaded Image" class="max-w-md h-auto border rounded">
        </div>
        {% endif %}

        <!-- Products List -->
        <div class="bg-white rounded-lg shadow p-6">
            <h2 class="text-lg font-semibold mb-4">Products ({{ products|length }})</h2>
            
            {% if products %}
                <div class="space-y-4">
                    {% for product in products %}
                    <div class="border rounded-lg p-4">
                        <div class="flex items-start space-x-4">
                            {% if product.images.exists %}
                            <img src="{{ product.images.first.image.url }}" alt="{{ product.name }}" 
                                 class="w-20 h-20 object-cover rounded">
                            {% else %}
                            <div class="w-20 h-20 bg-gray-200 rounded flex items-center justify-center">
                                <span class="text-gray-500 text-xs">No Image</span>
                            </div>
                            {% endif %}
                            
                            <div class="flex-1">
                                <h3 class="font-semibold">{{ product.name }}</h3>
                                <p class="text-sm text-gray-600">{{ product.description|truncatewords:20 }}</p>
                                <p class="text-sm text-gray-500">Category: {{ product.category.name|default:"None" }}</p>
                                <p class="font-semibold text-blue-600">{{ product.price }}đ</p>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="text-center py-8">
                    <p class="text-gray-500">No products found</p>
                    <p class="text-sm text-gray-400 mt-2">Products variable: {{ products }}</p>
                </div>
            {% endif %}
        </div>

        <!-- Raw Data -->
        <div class="bg-gray-100 rounded-lg p-6 mt-6">
            <h2 class="text-lg font-semibold mb-4">Raw Data</h2>
            <pre class="text-xs overflow-auto">
Context Variables:
- type: {{ type }}
- image_url: {{ image_url }}
- predicted_class: {{ predicted_class }}
- category_name: {{ category_name }}
- error_message: {{ error_message }}
- has_features: {{ has_features }}
- products: {{ products }}
- categories: {{ categories }}
            </pre>
        </div>
    </div>

    <script>
        function clearSession() {
            fetch('/search-by-image/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': '{{ csrf_token }}'
                },
                body: JSON.stringify({action: 'clear_session'})
            }).then(() => {
                location.reload();
            });
        }
    </script>
</body>
</html>
