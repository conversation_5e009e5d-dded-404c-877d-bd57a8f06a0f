from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth import login, authenticate, logout, update_session_auth_hash
from django.contrib.auth.forms import AuthenticationForm
from django.contrib.auth.decorators import login_required, user_passes_test
from django.contrib import messages
from django.core.mail import send_mail
from django.conf import settings
from django.core.files.storage import FileSystemStorage
from django.http import JsonResponse, HttpResponse
from django.core.paginator import Paginator, PageNotAnInteger, EmptyPage
from django.contrib.contenttypes.models import ContentType
from .models import Product, Category, Contact, ProductImage, Order, Activity, Cart, CartItem, OrderItem, Promotion, News, InventoryTransaction, User, ChatRoom, ChatMessage, Review
from .forms import RegisterForm, UserProfileForm, CustomPasswordChangeForm, ContactForm, ProductForm, NewsForm, InventoryTransactionForm, ReviewForm
from django.db.models import Sum, Q, F
from django.utils import timezone
from django.views.decorators.http import require_POST
from django.views.decorators.csrf import csrf_exempt
from .utils import classify_product, extract_features, calculate_similarity
import json
import os
import hmac
import hashlib
import requests
import uuid
from datetime import datetime, timedelta
from django.urls import reverse
from django.views.generic import ListView
from django.contrib.auth.mixins import LoginRequiredMixin
import openpyxl
from openpyxl.styles import Font, Alignment
from django.contrib.admin.views.decorators import staff_member_required
from openpyxl import Workbook
import csv
from django.conf import settings

# ZaloPay Configuration
ZALOPAY_APP_ID = settings.ZALOPAY_APP_ID
ZALOPAY_KEY1 = settings.ZALOPAY_KEY1
ZALOPAY_KEY2 = settings.ZALOPAY_KEY2
ZALOPAY_CREATE_ORDER_URL = "https://sandbox.zalopay.com.vn/v001/tpe/createorder"
ZALOPAY_CALLBACK_URL = "your_callback_url"

def generate_zalopay_order_data(amount, order_id):
    app_trans_id = f"{datetime.now().strftime('%y%m%d')}_{str(uuid.uuid4())[:8]}"
    data = {
        "app_id": ZALOPAY_APP_ID,
        "app_trans_id": app_trans_id,
        "app_user": "user123",
        "app_time": int(datetime.now().timestamp() * 1000),
        "amount": amount,
        "description": f"Thanh toan don hang #{order_id}",
        "bank_code": "",
        "callback_url": ZALOPAY_CALLBACK_URL,
        "item": json.dumps([{"order_id": order_id}])
    }
    
    # Generate MAC
    data_str = f"{data['app_id']}|{data['app_trans_id']}|{data['app_user']}|{data['amount']}|{data['app_time']}|{data['embed_data']}|{data['item']}"
    mac = hmac.new(ZALOPAY_KEY1.encode(), data_str.encode(), hashlib.sha256).hexdigest()
    data["mac"] = mac
    
    return data

def create_zalopay_order(request):
    if request.method == 'POST':
        try:
            # Get form data
            full_name = request.POST.get('full_name')
            phone = request.POST.get('phone')
            email = request.POST.get('email')
            address = request.POST.get('address')
            note = request.POST.get('note')
            
            # Create order in database with pending status
            order = Order.objects.create(
                user=request.user,
                full_name=full_name,
                phone=phone,
                email=email,
                address=address,
                note=note,
                payment_method='zalopay',
                status='pending'
            )
            
            # Get cart items and create order items
            cart = Cart.objects.get(user=request.user)
            total_amount = 0
            
            for cart_item in cart.items.all():
                OrderItem.objects.create(
                    order=order,
                    product=cart_item.product,
                    quantity=cart_item.quantity,
                    price=cart_item.product.price
                )
                total_amount += cart_item.quantity * cart_item.product.price

            # Generate ZaloPay order data
            embed_data = json.dumps({
                "order_id": str(order.id),
                "redirecturl": request.build_absolute_uri(reverse('order_detail', args=[order.id]))
            })

            items = json.dumps([{
                "itemid": str(item.product.id),
                "itemname": item.product.name,
                "itemprice": int(item.product.price),
                "itemquantity": item.quantity
            } for item in cart.items.all()])

            current_time = int(datetime.now().timestamp() * 1000)  # Milliseconds
            app_trans_id = f"{datetime.now().strftime('%y%m%d')}_{str(uuid.uuid4())[:8]}"

            # Prepare order data
            order_data = {
                "app_id": ZALOPAY_APP_ID,
                "app_trans_id": app_trans_id,
                "app_user": str(request.user.id),
                "app_time": current_time,
                "amount": total_amount,
                "description": f"Thanh toan don hang #{order.id}",
                "bank_code": "",
                "embed_data": embed_data,
                "item": items,
                "callback_url": request.build_absolute_uri(reverse('zalopay_callback'))
            }

            # Generate MAC
            data_str = f"{order_data['app_id']}|{order_data['app_trans_id']}|{order_data['app_user']}|{order_data['amount']}|{order_data['app_time']}|{order_data['embed_data']}|{order_data['item']}"
            order_data["mac"] = hmac.new(
                ZALOPAY_KEY1.encode(), 
                data_str.encode(), 
                hashlib.sha256
            ).hexdigest()

            # Call ZaloPay API
            response = requests.post(ZALOPAY_CREATE_ORDER_URL, json=order_data)
            zalopay_response = response.json()

            if zalopay_response.get('return_code') == 1:
                # Update order with ZaloPay transaction info
                order.transaction_id = app_trans_id
                order.save()

                return JsonResponse({
                    'code': 1,
                    'message': 'Tạo đơn hàng thành công',
                    'order_url': zalopay_response.get('order_url')
                })
            else:
                # Delete order if ZaloPay creation fails
                order.delete()
                return JsonResponse({
                    'code': 0,
                    'message': 'Không thể tạo đơn hàng ZaloPay'
                })

        except Exception as e:
            return JsonResponse({
                'code': 0,
                'message': str(e)
            })

    return JsonResponse({
        'code': 0,
        'message': 'Phương thức không được hỗ trợ'
    })

@csrf_exempt
def zalopay_callback(request):
    if request.method == 'POST':
        try:
            data = json.loads(request.body)
            
            # Verify callback data
            data_str = f"{data['app_id']}|{data['app_trans_id']}|{data['app_user']}|{data['amount']}|{data['app_time']}|{data['embed_data']}|{data['item']}"
            mac = hmac.new(
                ZALOPAY_KEY2.encode(), 
                data_str.encode(), 
                hashlib.sha256
            ).hexdigest()
            
            if mac != data.get('mac'):
                return JsonResponse({'return_code': 0, 'return_message': 'Invalid MAC'})
            
            # Get order from embed_data
            embed_data = json.loads(data.get('embed_data', '{}'))
            order_id = embed_data.get('order_id')
            
            if not order_id:
                return JsonResponse({'return_code': 0, 'return_message': 'Invalid order data'})
            
            order = Order.objects.get(id=order_id)
            
            if data.get('status') == 1:  # Payment successful
                # Update order status
                order.status = 'paid'
                order.save()
                
                # Update product stock
                for item in order.items.all():
                    product = item.product
                    product.stock -= item.quantity
                    product.save()
                
                # Clear cart
                Cart.objects.filter(user=order.user).delete()
                
                return JsonResponse({'return_code': 1, 'return_message': 'Success'})
            else:
                order.status = 'cancelled'
                order.save()
                return JsonResponse({'return_code': 0, 'return_message': 'Payment failed'})
                
        except Exception as e:
            return JsonResponse({'return_code': 0, 'return_message': str(e)})
    
    return JsonResponse({'return_code': 0, 'return_message': 'Invalid request method'})

# Create your views here.

def home(request):
    from .recommendations import get_product_recommendations, get_trending_products
    
    featured_products = Product.objects.filter(is_available=True)[:8]
    categories = Category.objects.all()
    
    # Lấy sản phẩm gợi ý
    recommended_products = get_product_recommendations(request.user, num_products=4)
    
    # Lấy sản phẩm thịnh hành
    trending_products = get_trending_products(num_products=4)
    
    context = {
        'featured_products': featured_products,
        'categories': categories,
        'recommended_products': recommended_products,
        'trending_products': trending_products,
        'cart_count': get_cart_count(request)
    }
    return render(request, 'core/home.html', context)

def register_view(request):
    if request.user.is_authenticated:
        return redirect('home')
    
    if request.method == "POST":
        form = RegisterForm(request.POST)
        if form.is_valid():
            user = form.save()
            login(request, user)
            messages.success(request, "Đăng ký tài khoản thành công!")
            return redirect('home')
        else:
            for error in list(form.errors.values()):
                messages.error(request, error)
    else:
        form = RegisterForm()
        
    return render(request, 'core/register.html', {'form': form})

def login_view(request):
    if request.user.is_authenticated:
        return redirect('home')
    
    if request.method == "POST":
        form = AuthenticationForm(request, data=request.POST)
        if form.is_valid():
            username = form.cleaned_data.get('username')
            password = form.cleaned_data.get('password')
            user = authenticate(username=username, password=password)
            if user is not None:
                login(request, user)
                messages.success(request, f"Chào mừng {username} đã quay trở lại!")
                return redirect('home')
            else:
                messages.error(request, "Tên đăng nhập hoặc mật khẩu không đúng!")
        else:
            messages.error(request, "Tên đăng nhập hoặc mật khẩu không đúng!")
    else:
        form = AuthenticationForm()
        
    return render(request, 'core/login.html', {'form': form})

def logout_view(request):
    logout(request)
    messages.success(request, "Đăng xuất thành công!")
    return redirect('home')

@login_required
def profile_view(request):
    if request.method == 'POST':
        form = UserProfileForm(request.POST, instance=request.user)
        if form.is_valid():
            form.save()
            messages.success(request, 'Thông tin tài khoản đã được cập nhật!')
            return redirect('profile')
    else:
        form = UserProfileForm(instance=request.user)
    
    context = {
        'form': form,
        'user': request.user
    }
    return render(request, 'core/profile.html', context)

@login_required
def change_password_view(request):
    if request.method == 'POST':
        form = CustomPasswordChangeForm(request.user, request.POST)
        if form.is_valid():
            user = form.save()
            # Cập nhật session để người dùng không bị đăng xuất
            update_session_auth_hash(request, user)
            messages.success(request, 'Mật khẩu của bạn đã được thay đổi thành công!')
            return redirect('profile')
        else:
            for error in list(form.errors.values()):
                messages.error(request, error)
    else:
        form = CustomPasswordChangeForm(request.user)
    
    return render(request, 'core/change_password.html', {'form': form})

def upload_image_ai(request):
    # Khởi tạo các biến mặc định
    image_url = None
    similar_products = []
    predicted_class = None
    category_name = None
    error_message = None
    query_features = None
    categories = Category.objects.all()

    print(f"DEBUG: Request method: {request.method}")
    print(f"DEBUG: Request FILES: {request.FILES}")
    print(f"DEBUG: Has image file: {request.FILES.get('image') is not None}")

    # Chỉ xử lý POST request với file upload
    if request.method == 'POST' and request.FILES.get('image'):
        try:
            uploaded_file = request.FILES['image']
            print(f"DEBUG: Uploaded file name: {uploaded_file.name}")
            print(f"DEBUG: Uploaded file size: {uploaded_file.size}")

            fs = FileSystemStorage()
            filename = fs.save(uploaded_file.name, uploaded_file)
            image_url = fs.url(filename)
            img_path = os.path.join(fs.location, filename)

            print(f"DEBUG: Saved filename: {filename}")
            print(f"DEBUG: Image URL: {image_url}")
            print(f"DEBUG: Image path: {img_path}")

            # Xử lý ảnh
            predicted_class, category_name, query_features, similar_products = process_uploaded_image(img_path)

            print(f"DEBUG: Predicted class: {predicted_class}")
            print(f"DEBUG: Category name: {category_name}")
            print(f"DEBUG: Similar products count: {len(similar_products)}")

        except Exception as e:
            error_message = f"Lỗi xử lý ảnh: {str(e)}"
            print(f"DEBUG: Exception occurred: {e}")
            import traceback
            traceback.print_exc()
    else:
        print("DEBUG: Not a POST request with image file")

    return render(request, 'core/search_image.html', {
        'image_url': image_url,
        'categories': categories,
        'products': similar_products,
        'predicted_class': predicted_class,
        'category_name': category_name,
        'error_message': error_message,
        'has_features': query_features is not None,
        'type': "search_by_image"
    })


def process_uploaded_image(img_path):
    """
    Hàm helper để xử lý ảnh đã upload
    """
    try:
        # Bước 1: Phân loại sản phẩm
        predicted_class, category_name = classify_product(img_path)

        # Bước 2: Trích xuất đặc trưng từ ảnh upload
        query_features = extract_features(img_path)

        # Bước 3: Tìm sản phẩm tương tự
        similar_products = []
        if category_name != "Lỗi hệ thống" and query_features is not None:
            # Tìm hoặc tạo danh mục
            # category, created = Category.objects.get_or_create(name=category_name)


            products_in_category = Product.objects.filter(is_available=True)
            print(f"DEBUG: Found {products_in_category.count()} available products")

            # Tính toán độ tương đồng cho từng sản phẩm
            products_with_similarity = []
            for product in products_in_category:
                # Tạm thời sử dụng ảnh đầu tiên của sản phẩm để trích xuất features
                if product.images.exists():
                    try:
                        first_image = product.images.first()
                        product_image_path = first_image.image.path
                        print(f"DEBUG: Extracting features for product {product.name}")
                        product_features = extract_features(product_image_path)

                        if product_features is not None:
                            similarity = calculate_similarity(query_features, product_features)
                            products_with_similarity.append((product, similarity))
                            print(f"DEBUG: Similarity for {product.name}: {similarity}")
                    except Exception as e:
                        print(f"DEBUG: Error processing product {product.name}: {e}")
                        continue

            # Sắp xếp theo độ tương đồng giảm dần và lấy top 5
            products_with_similarity.sort(key=lambda x: x[1], reverse=True)
            similar_products = [item[0] for item in products_with_similarity[:5]]
            print(f"DEBUG: Found {len(similar_products)} similar products")

        return predicted_class, category_name, query_features, similar_products

    except Exception as e:
        raise Exception(f"Lỗi trong quá trình xử lý ảnh: {str(e)}")

def about_view(request):
    context = {
        'team_members': [
            {
                'name': 'Lê Minh Hằng',
                'position': 'Creative Director',
                'image': 'team/member1.jpg',
                'description': 'Với đam mê sáng tạo và 8 năm kinh nghiệm trong ngành thời trang, dẫn dắt xu hướng và thiết kế độc đáo.'
            },
            {
                'name': 'Trần Thùy Linh',
                'position': 'Head Stylist',
                'image': 'team/member2.jpg',
                'description': 'Chuyên gia tư vấn phong cách với khả năng phối đồ tinh tế cho mọi dáng người và cá tính.'
            },
            {
                'name': 'Nguyễn Hoàng Nam',
                'position': 'Brand Manager',
                'image': 'team/member3.jpg',
                'description': 'Đam mê xây dựng thương hiệu thời trang trẻ trung, hiện đại và gần gũi với giới trẻ Việt.'
            }
        ],
        'milestones': [
            {
                'year': '2019',
                'title': 'Khởi nghiệp Fashion Elite',
                'description': 'Fashion Elite ra đời với ước mơ mang đến phong cách thời trang hiện đại cho giới trẻ Việt.'
            },
            {
                'year': '2020',
                'title': 'Showroom đầu tiên',
                'description': 'Khai trương showroom đầu tiên tại trung tâm Sài Gòn, thu hút hàng nghìn fashionista.'
            },
            {
                'year': '2021',
                'title': 'Bộ sưu tập đột phá',
                'description': 'Ra mắt bộ sưu tập "Urban Chic" - định hình phong cách street fashion Việt Nam.'
            },
            {
                'year': '2022',
                'title': 'Mở rộng toàn quốc',
                'description': 'Phát triển chuỗi 25+ cửa hàng trên khắp Việt Nam với hơn 2000+ thiết kế độc quyền.'
            },
            {
                'year': '2023',
                'title': 'Fashion Elite Online',
                'description': 'Ra mắt nền tảng mua sắm trực tuyến và ứng dụng tư vấn phong cách AI.'
            }
        ]
    }
    return render(request, 'core/about.html', context)

def contact_view(request):
    if request.method == 'POST':
        form = ContactForm(request.POST)
        if form.is_valid():
            # Lưu thông tin vào database
            contact = Contact.objects.create(
                name=form.cleaned_data['name'],
                email=form.cleaned_data['email'],
                subject=form.cleaned_data['subject'],
                message=form.cleaned_data['message']
            )
            
            # Gửi email thông báo
            try:
                # Email cho admin
                admin_subject = f"Liên hệ mới từ: {contact.name}"
                admin_message = f"""
                Thông tin liên hệ mới:
                
                Họ tên: {contact.name}
                Email: {contact.email}
                Tiêu đề: {contact.subject}
                
                Nội dung:
                {contact.message}
                """
                send_mail(
                    admin_subject,
                    admin_message,
                    settings.DEFAULT_FROM_EMAIL,
                    [settings.CONTACT_EMAIL],
                    fail_silently=True,
                )

                # Email phản hồi tự động cho khách hàng
                user_subject = "Cảm ơn bạn đã liên hệ với ElectroMart"
                user_message = f"""
                Xin chào {contact.name},

                Cảm ơn bạn đã liên hệ với ElectroMart. Chúng tôi đã nhận được tin nhắn của bạn và sẽ phản hồi trong thời gian sớm nhất.

                Nội dung tin nhắn của bạn:
                {contact.subject}

                Trân trọng,
                Đội ngũ ElectroMart
                """
                send_mail(
                    user_subject,
                    user_message,
                    settings.DEFAULT_FROM_EMAIL,
                    [contact.email],
                    fail_silently=True,
                )
            except Exception as e:
                print(f"Error sending email: {e}")
                # Vẫn hiện thông báo thành công vì đã lưu được vào database
                
            messages.success(request, 'Cảm ơn bạn đã liên hệ với chúng tôi! Chúng tôi sẽ phản hồi sớm nhất có thể.')
            return redirect('contact')
    else:
        form = ContactForm()
    
    context = {
        'form': form,
        'contact_info': {
            'address': '123 Đường ABC, Quận XYZ, TP.HCM',
            'phone': '1900 1234',
            'email': '<EMAIL>',
            'working_hours': 'Thứ 2 - Chủ nhật: 8:00 - 21:00'
        }
    }
    return render(request, 'core/contact.html', context)

def is_staff(user):
    return user.is_authenticated and user.is_staff

@user_passes_test(is_staff)
def admin_dashboard(request):
    # Thống kê liên hệ mới
    unprocessed_contacts_count = Contact.objects.filter(is_processed=False).count()
    
    # Thống kê danh mục và sản phẩm
    categories_count = Category.objects.count()
    products_count = Product.objects.count()
    
    # Thống kê doanh thu tháng này (chỉ tính đơn đã hoàn thành)
    today = timezone.now()
    start_of_month = today.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
    monthly_revenue = Order.objects.filter(
        created_at__gte=start_of_month,
        status='completed'
    ).aggregate(
        total=Sum(F('subtotal') + F('shipping_fee') - F('discount'))
    )['total'] or 0
    
    # Thống kê đơn hàng theo trạng thái
    processing_orders = Order.objects.filter(
        created_at__gte=start_of_month,
        status='processing'
    ).count()
    
    shipping_orders = Order.objects.filter(
        created_at__gte=start_of_month,
        status='shipping'
    ).count()
    
    completed_orders = Order.objects.filter(
        created_at__gte=start_of_month,
        status='completed'
    ).count()
    
    # Tổng số đơn hàng đang xử lý trở lên (không tính pending và cancelled)
    total_valid_orders = processing_orders + shipping_orders + completed_orders
    
    # Thống kê người dùng
    total_users = User.objects.count()
    new_users_this_month = User.objects.filter(date_joined__gte=start_of_month).count()
    
    # Thống kê sản phẩm bán chạy (chỉ tính các đơn hàng đã hoàn thành)
    top_products = OrderItem.objects.filter(
        order__created_at__gte=start_of_month,
        order__status='completed'
    ).values(
        'product__id',
        'product__name'
    ).annotate(
        total_quantity=Sum('quantity'),
        total_revenue=Sum(F('quantity') * F('price'))
    ).order_by('-total_quantity')[:5]
    
    # Lấy thông tin hình ảnh cho các sản phẩm top
    for product in top_products:
        first_image = ProductImage.objects.filter(product_id=product['product__id']).first()
        product['image_url'] = first_image.image.url if first_image else None
    
    # Hoạt động gần đây
    recent_activities = Activity.objects.select_related('user').order_by('-timestamp')[:10]
    
    context = {
        'unprocessed_contacts_count': unprocessed_contacts_count,
        'categories_count': categories_count,
        'products_count': products_count,
        'monthly_revenue': monthly_revenue,
        'processing_orders': processing_orders,
        'shipping_orders': shipping_orders,
        'completed_orders': completed_orders,
        'total_valid_orders': total_valid_orders,
        'total_users': total_users,
        'new_users_this_month': new_users_this_month,
        'top_products': top_products,
        'recent_activities': recent_activities,
        'start_of_month': start_of_month.strftime('%d/%m/%Y'),
        'end_of_month': today.strftime('%d/%m/%Y')
    }
    return render(request, 'admin/dashboard.html', context)

@user_passes_test(is_staff)
def admin_contacts(request):
    search = request.GET.get('search', '')
    status = request.GET.get('status', '')
    export = request.GET.get('export', '')
    
    contacts = Contact.objects.all()
    
    if search:
        contacts = contacts.filter(
            Q(name__icontains=search) |
            Q(email__icontains=search) |
            Q(subject__icontains=search) |
            Q(message__icontains=search)
        )
    
    if status:
        is_processed = status == 'processed'
        contacts = contacts.filter(is_processed=is_processed)
    
    if export == 'excel':
        # Tạo workbook và worksheet mới
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "Danh sách liên hệ"
        
        # Thiết lập header
        headers = ['STT', 'Họ tên', 'Email', 'Tiêu đề', 'Nội dung', 'Trạng thái', 'Thời gian', 'Phản hồi']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col)
            cell.value = header
            cell.font = Font(bold=True)
            cell.alignment = Alignment(horizontal='center')
        
        # Điền dữ liệu
        for row, contact in enumerate(contacts, 2):
            ws.cell(row=row, column=1, value=row-1)  # STT
            ws.cell(row=row, column=2, value=contact.name)
            ws.cell(row=row, column=3, value=contact.email)
            ws.cell(row=row, column=4, value=contact.subject)
            ws.cell(row=row, column=5, value=contact.message)
            ws.cell(row=row, column=6, value='Đã xử lý' if contact.is_processed else 'Chưa xử lý')
            ws.cell(row=row, column=7, value=contact.created_at.strftime('%d/%m/%Y %H:%M'))
            ws.cell(row=row, column=8, value=contact.response or '')
        
        # Điều chỉnh độ rộng cột
        for col in ws.columns:
            max_length = 0
            column = col[0].column_letter
            for cell in col:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = (max_length + 2)
            ws.column_dimensions[column].width = min(adjusted_width, 50)
        
        # Tạo response
        response = HttpResponse(
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = f'attachment; filename=danh-sach-lien-he-{datetime.now().strftime("%Y%m%d")}.xlsx'
        
        wb.save(response)
        return response
    
    paginator = Paginator(contacts, 10)
    page = request.GET.get('page')
    contacts = paginator.get_page(page)
    
    return render(request, 'admin/contacts.html', {'contacts': contacts})

@user_passes_test(is_staff)
def admin_contact_detail(request, contact_id):
    try:
        contact = Contact.objects.get(id=contact_id)
        return JsonResponse({
            'id': contact.id,
            'name': contact.name,
            'email': contact.email,
            'subject': contact.subject,
            'message': contact.message,
            'response': contact.response or '',
            'is_processed': contact.is_processed,
            'created_at': contact.created_at.strftime('%d/%m/%Y %H:%M')
        })
    except Contact.DoesNotExist:
        return JsonResponse({'error': 'Không tìm thấy liên hệ'}, status=404)

@user_passes_test(is_staff)
def admin_contact_respond(request, contact_id):
    if request.method != 'POST':
        return JsonResponse({'error': 'Phương thức không được hỗ trợ'}, status=405)
    
    try:
        data = json.loads(request.body)
        contact = Contact.objects.get(id=contact_id)
        contact.response = data.get('response', '').strip()
        contact.is_processed = True
        contact.save()
        
        # Gửi email phản hồi
        subject = f'Phản hồi: {contact.subject}'
        message = f'''
        Xin chào {contact.name},

        Cảm ơn bạn đã liên hệ với chúng tôi. Dưới đây là phản hồi cho yêu cầu của bạn:

        {contact.response}

        Trân trọng,
        ElectroMart
        '''
        send_mail(
            subject,
            message,
            settings.DEFAULT_FROM_EMAIL,
            [contact.email],
            fail_silently=True
        )
        
        return JsonResponse({'success': True})
    except Contact.DoesNotExist:
        return JsonResponse({'error': 'Không tìm thấy liên hệ'}, status=404)
    except json.JSONDecodeError:
        return JsonResponse({'error': 'Dữ liệu không hợp lệ'}, status=400)
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)

@user_passes_test(is_staff)
def admin_contact_delete(request, contact_id):
    if request.method != 'POST':
        return JsonResponse({'error': 'Phương thức không được hỗ trợ'}, status=405)
    
    try:
        contact = Contact.objects.get(id=contact_id)
        contact.delete()
        return JsonResponse({'success': True})
    except Contact.DoesNotExist:
        return JsonResponse({'error': 'Không tìm thấy liên hệ'}, status=404)

def contact(request):
    if request.method == 'POST':
        form = ContactForm(request.POST)
        if form.is_valid():
            contact = form.save()
            messages.success(request, 'Cảm ơn bạn đã liên hệ. Chúng tôi sẽ phản hồi sớm nhất có thể.')
            return redirect('contact')
    else:
        form = ContactForm()
    
    return render(request, 'core/contact.html', {'form': form})

@user_passes_test(is_staff)
def admin_categories(request):
    search_query = request.GET.get('search', '')
    categories = Category.objects.all()
    
    if search_query:
        categories = categories.filter(name__icontains=search_query)
    
    paginator = Paginator(categories, 10)  # 10 items per page
    page = request.GET.get('page')
    categories = paginator.get_page(page)
    
    context = {
        'categories': categories,
        'active_tab': 'categories'
    }
    return render(request, 'admin/categories.html', context)

@user_passes_test(is_staff)
def admin_category_create(request):
    if request.method == 'POST':
        try:
            name = request.POST.get('name')
            description = request.POST.get('description', '')
            image = request.FILES.get('image')
            
            # Validation
            if not name:
                return JsonResponse({
                    'success': False,
                    'errors': {'name': 'Tên danh mục không được để trống'}
                })
            
            # Check if category with this name already exists
            if Category.objects.filter(name=name).exists():
                return JsonResponse({
                    'success': False,
                    'errors': {'name': 'Danh mục này đã tồn tại'}
                })
            
            category = Category.objects.create(
                name=name,
                description=description,
                image=image
            )
            
            # Log activity
            category_type = ContentType.objects.get_for_model(Category)
            Activity.objects.create(
                user=request.user,
                action='create',
                content_type=category_type,
                object_id=category.id,
                description=f'Tạo danh mục mới: {category.name}'
            )
            
            return JsonResponse({
                'success': True,
                'message': 'Thêm danh mục thành công'
            })
            
        except Exception as e:
            return JsonResponse({
                'success': False,
                'message': str(e)
            })
    
    return JsonResponse({
        'success': False,
        'message': 'Method not allowed'
    }, status=405)

@user_passes_test(is_staff)
def admin_category_detail(request, category_id):
    try:
        category = get_object_or_404(Category, id=category_id)
        return JsonResponse({
            'id': category.id,
            'name': category.name,
            'description': category.description,
            'image': category.image.url if category.image else None
        })
    except Category.DoesNotExist:
        return JsonResponse({
            'success': False,
            'message': 'Không tìm thấy danh mục'
        }, status=404)

@user_passes_test(is_staff)
def admin_category_update(request, category_id):
    if request.method == 'POST':
        try:
            category = get_object_or_404(Category, id=category_id)
            name = request.POST.get('name')
            description = request.POST.get('description', '')
            image = request.FILES.get('image')
            
            # Validation
            if not name:
                return JsonResponse({
                    'success': False,
                    'errors': {'name': 'Tên danh mục không được để trống'}
                })
            
            # Check if category with this name already exists (excluding current category)
            if Category.objects.filter(name=name).exclude(id=category_id).exists():
                return JsonResponse({
                    'success': False,
                    'errors': {'name': 'Danh mục này đã tồn tại'}
                })
            
            # Update fields
            category.name = name
            category.description = description
            if image:
                category.image = image
            category.save()
            
            # Log activity
            category_type = ContentType.objects.get_for_model(Category)
            Activity.objects.create(
                user=request.user,
                action='update',
                content_type=category_type,
                object_id=category.id,
                description=f'Cập nhật danh mục: {category.name}'
            )
            
            return JsonResponse({
                'success': True,
                'message': 'Cập nhật danh mục thành công'
            })
            
        except Category.DoesNotExist:
            return JsonResponse({
                'success': False,
                'message': 'Không tìm thấy danh mục'
            }, status=404)
        except Exception as e:
            return JsonResponse({
                'success': False,
                'message': str(e)
            })
    
    return JsonResponse({
        'success': False,
        'message': 'Method not allowed'
    }, status=405)

@user_passes_test(is_staff)
def admin_category_delete(request, category_id):
    if request.method == 'POST':
        try:
            category = get_object_or_404(Category, id=category_id)
            name = category.name
            category_type = ContentType.objects.get_for_model(Category)
            
            # Delete the category
            category.delete()
            
            # Log activity
            Activity.objects.create(
                user=request.user,
                action='delete',
                content_type=category_type,
                object_id=category_id,
                description=f'Xóa danh mục: {name}'
            )
            
            return JsonResponse({
                'success': True,
                'message': 'Xóa danh mục thành công'
            })
            
        except Category.DoesNotExist:
            return JsonResponse({
                'success': False,
                'message': 'Không tìm thấy danh mục'
            }, status=404)
        except Exception as e:
            return JsonResponse({
                'success': False,
                'message': str(e)
            })
    
    return JsonResponse({
        'success': False,
        'message': 'Method not allowed'
    }, status=405)

@user_passes_test(is_staff)
def admin_products(request):
    products = Product.objects.select_related('category').all()
    
    # Filtering
    category_id = request.GET.get('category')
    if category_id:
        products = products.filter(category_id=category_id)

    status = request.GET.get('status')
    if status == '1':
        products = products.filter(is_available=True)
    elif status == '0':
        products = products.filter(is_available=False)

    # Search
    search = request.GET.get('search')
    if search:
        products = products.filter(
            Q(name__icontains=search) |
            Q(description__icontains=search) |
            Q(category__name__icontains=search)
        ).distinct()

    # Sorting
    sort = request.GET.get('sort')
    if sort == 'name':
        products = products.order_by('name')
    elif sort == 'price_asc':
        products = products.order_by('price')
    elif sort == 'price_desc':
        products = products.order_by('-price')
    elif sort == 'newest':
        products = products.order_by('-created_at')
    else:
        products = products.order_by('-created_at')  # Default sorting

    # Pagination
    paginator = Paginator(products, 10)
    page = request.GET.get('page')
    products = paginator.get_page(page)

    context = {
        'active_tab': 'products',
        'products': products,
        'categories': Category.objects.all(),
        'current_category': category_id,
        'current_status': status,
        'current_sort': sort,
        'search_query': search
    }
    return render(request, 'admin/products.html', context)

@user_passes_test(is_staff)
def admin_product_create(request):
    if request.method == 'POST':
        form = ProductForm(request.POST, request.FILES)
        
        # Kiểm tra xem có dữ liệu specifications từ JavaScript không
        specifications_json = request.POST.get('specifications')
        if specifications_json:
            # Đặt flag để form không ghi đè specifications
            form._skip_specifications = True
        
        if form.is_valid():
            product = form.save()
            
            # Xử lý thông số kỹ thuật từ JavaScript
            if specifications_json:
                try:
                    import json
                    specifications = json.loads(specifications_json)
                    # Chỉ lưu các thông số có giá trị và convert space sang underscore cho Django template
                    filtered_specs = {}
                    for k, v in specifications.items():
                        if v:  # chỉ lưu các giá trị có content
                            # Convert space sang underscore để dùng trong Django template
                            key = k.replace(' ', '_')
                            filtered_specs[key] = v
                    product.specifications = filtered_specs
                    product.save()
                except json.JSONDecodeError:
                    pass  # Nếu lỗi JSON thì bỏ qua
            
            # Xử lý nhiều ảnh
            images = request.FILES.getlist('images')
            for image in images:
                ProductImage.objects.create(product=product, image=image)
            
            messages.success(request, 'Thêm sản phẩm thành công')
            return redirect('admin_products')
    else:
        form = ProductForm()
    
    categories = Category.objects.all()
    return render(request, 'admin/product_form.html', {
        'form': form,
        'categories': categories
    })

@user_passes_test(is_staff)
def admin_product_edit(request, product_id):
    product = get_object_or_404(Product, id=product_id)
    
    if request.method == 'POST':
        form = ProductForm(request.POST, request.FILES, instance=product)
        
        # Kiểm tra xem có dữ liệu specifications từ JavaScript không
        specifications_json = request.POST.get('specifications')
        if specifications_json:
            # Đặt flag để form không ghi đè specifications
            form._skip_specifications = True
        
        if form.is_valid():
            product = form.save()
            
            # Xử lý thông số kỹ thuật từ JavaScript
            if specifications_json:
                try:
                    import json
                    specifications = json.loads(specifications_json)
                    # Chỉ lưu các thông số có giá trị và convert space sang underscore cho Django template
                    filtered_specs = {}
                    for k, v in specifications.items():
                        if v:  # chỉ lưu các giá trị có content
                            # Convert space sang underscore để dùng trong Django template
                            key = k.replace(' ', '_')
                            filtered_specs[key] = v
                    product.specifications = filtered_specs
                    product.save()
                except json.JSONDecodeError:
                    pass  # Nếu lỗi JSON thì bỏ qua
            
            # Xử lý nhiều ảnh
            images = request.FILES.getlist('images')
            for image in images:
                ProductImage.objects.create(product=product, image=image)
            
            messages.success(request, 'Cập nhật sản phẩm thành công')
            return redirect('admin_products')
    else:
        form = ProductForm(instance=product)
    
    categories = Category.objects.all()
    return render(request, 'admin/product_form.html', {
        'form': form,
        'product': product,
        'categories': categories
    })

@user_passes_test(is_staff)
def admin_product_delete(request, product_id):
    product = get_object_or_404(Product, id=product_id)
    
    if request.method == 'POST':
        try:
            product_name = product.name
            
            # Xóa tất cả hình ảnh liên quan
            ProductImage.objects.filter(product=product).delete()
            
            # Xóa tất cả đánh giá liên quan
            Review.objects.filter(product=product).delete()
            
            # Xóa sản phẩm khỏi giỏ hàng
            CartItem.objects.filter(product=product).delete()
            
            # Cập nhật OrderItem thay vì xóa để giữ lịch sử
            OrderItem.objects.filter(product=product).update(product=None)
            
            # Xóa sản phẩm
            product.delete()
            
            # Log activity
            product_type = ContentType.objects.get_for_model(Product)
            Activity.objects.create(
                user=request.user,
                action='delete',
                content_type=product_type,
                object_id=product_id,
                description=f'Đã xóa sản phẩm: {product_name}'
            )
            
            messages.success(request, f'Đã xóa sản phẩm "{product_name}" thành công!')
            return redirect('admin_products')
            
        except Exception as e:
            messages.error(request, f'Có lỗi xảy ra khi xóa sản phẩm: {str(e)}')
            return redirect('admin_products')
    
    # GET request - hiển thị trang xác nhận xóa
    context = {
        'product': product,
        'active_tab': 'products'
    }
    return render(request, 'admin/product_delete.html', context)

@user_passes_test(is_staff)
@require_POST
def admin_product_image_delete(request, image_id):
    image = get_object_or_404(ProductImage, id=image_id)
    try:
        image.delete()
        return JsonResponse({
            'success': True,
            'message': 'Xóa ảnh thành công'
        })
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': str(e)
        }, status=400)

def product_detail(request, product_id):
    product = get_object_or_404(Product, id=product_id)
    related_products = Product.objects.filter(category=product.category).exclude(id=product_id)[:5]
    reviews = product.reviews.all().order_by('-created_at')
    
    review_form = None
    user_review = None
    
    if request.user.is_authenticated:
        user_review = reviews.filter(user=request.user).first()
        if not user_review:
            review_form = ReviewForm()
            
            if request.method == 'POST':
                review_form = ReviewForm(request.POST)
                if review_form.is_valid():
                    review = review_form.save(commit=False)
                    review.product = product
                    review.user = request.user
                    review.save()
                    messages.success(request, 'Đánh giá của bạn đã được gửi thành công!')
                    return redirect('product_detail', product_id=product.id)
    
    context = {
        'product': product,
        'related_products': related_products,
        'reviews': reviews,
        'review_form': review_form,
        'user_review': user_review,
    }
    return render(request, 'core/product_detail.html', context)

def get_cart_count(request):
    if request.user.is_authenticated:
        cart = Cart.objects.filter(user=request.user).first()
        if cart:
            return cart.items.count()
    return 0

@login_required
def cart_view(request):
    cart = Cart.objects.filter(user=request.user).first()
    if not cart:
        cart = Cart.objects.create(user=request.user)
    
    context = {
        'cart_items': cart.items.all(),
        'subtotal': cart.get_subtotal(),
        'shipping_fee': cart.get_shipping_fee(),
        'discount': cart.get_discount(),
        'total': cart.get_total(),
        'cart_count': get_cart_count(request)
    }
    return render(request, 'core/cart.html', context)

@require_POST
def add_to_cart(request, product_id):
    if not request.user.is_authenticated:
        return JsonResponse({'status': 'error', 'message': 'Vui lòng đăng nhập để thêm vào giỏ hàng'})
    
    product = get_object_or_404(Product, id=product_id)
    cart = Cart.objects.filter(user=request.user).first()
    if not cart:
        cart = Cart.objects.create(user=request.user)
    
    quantity = int(request.POST.get('quantity', 1))
    
    # Check if product already in cart
    cart_item = cart.items.filter(product=product).first()
    if cart_item:
        cart_item.quantity += quantity
        cart_item.save()
    else:
        CartItem.objects.create(
            cart=cart,
            product=product,
            quantity=quantity
        )
    
    return JsonResponse({
        'status': 'success',
        'message': 'Đã thêm sản phẩm vào giỏ hàng',
        'cart_count': get_cart_count(request)
    })

@require_POST
def remove_from_cart(request, product_id):
    if not request.user.is_authenticated:
        return JsonResponse({'status': 'error', 'message': 'Vui lòng đăng nhập'})
    
    cart = Cart.objects.filter(user=request.user).first()
    if cart:
        cart.items.filter(product_id=product_id).delete()
        return JsonResponse({
            'status': 'success',
            'message': 'Đã xóa sản phẩm khỏi giỏ hàng',
            'cart_count': get_cart_count(request)
        })
    return JsonResponse({'status': 'error', 'message': 'Không tìm thấy sản phẩm trong giỏ hàng'})

@require_POST
def update_cart(request, product_id):
    if not request.user.is_authenticated:
        return JsonResponse({'status': 'error', 'message': 'Vui lòng đăng nhập'})
    
    try:
        data = json.loads(request.body)
        quantity = int(data.get('quantity', 1))
    except:
        return JsonResponse({'status': 'error', 'message': 'Dữ liệu không hợp lệ'})
    
    if quantity < 1:
        return JsonResponse({'status': 'error', 'message': 'Số lượng phải lớn hơn 0'})
    
    cart = Cart.objects.filter(user=request.user).first()
    if cart:
        cart_item = cart.items.filter(product_id=product_id).first()
        if cart_item:
            if quantity > cart_item.product.stock:
                return JsonResponse({'status': 'error', 'message': 'Số lượng vượt quá hàng tồn kho'})
            
            cart_item.quantity = quantity
            cart_item.save()
            return JsonResponse({
                'status': 'success',
                'message': 'Đã cập nhật số lượng',
                'cart_count': get_cart_count(request)
            })
    return JsonResponse({'status': 'error', 'message': 'Không tìm thấy sản phẩm trong giỏ hàng'})

def category_detail(request, category_slug):
    category = get_object_or_404(Category, slug=category_slug)
    products = Product.objects.filter(category=category, is_available=True)
    
    # Pagination
    page = request.GET.get('page', 1)
    paginator = Paginator(products, 12)  # 12 products per page
    
    try:
        products = paginator.page(page)
    except PageNotAnInteger:
        products = paginator.page(1)
    except EmptyPage:
        products = paginator.page(paginator.num_pages)
    
    context = {
        'category': category,
        'products': products,
    }
    return render(request, 'core/category.html', context)

@login_required
def checkout(request):
    cart = Cart.objects.filter(user=request.user).first()
    if not cart or cart.items.count() == 0:
        messages.error(request, 'Giỏ hàng của bạn đang trống')
        return redirect('cart')
    
    if request.method == 'POST':
        try:
            # Generate order number
            order_number = f"ORD{timezone.now().strftime('%Y%m%d%H%M%S')}{request.user.id}"
            
            # Log form data
            print("Form Data:", request.POST)
            
            # Create order
            order = Order.objects.create(
                user=request.user,
                order_number=order_number,
                full_name=request.POST.get('full_name'),
                phone=request.POST.get('phone'),
                email=request.POST.get('email'),
                address=request.POST.get('address'),
                note=request.POST.get('note', ''),
                status='pending',
                payment_method=request.POST.get('payment_method'),
                payment_status='pending',
                subtotal=cart.get_subtotal(),
                shipping_fee=cart.get_shipping_fee(),
                discount=cart.get_discount(),
                promotion=cart.promotion
            )
            
            print("Order created successfully:", order.id)
            
            # Create order items
            for cart_item in cart.items.all():
                print(f"Processing cart item: {cart_item.product.name}, quantity: {cart_item.quantity}")
                
                OrderItem.objects.create(
                    order=order,
                    product=cart_item.product,
                    quantity=cart_item.quantity,
                    price=cart_item.product.price
                )
                
                # Update product stock
                product = cart_item.product
                print(f"Updating stock for product {product.name}: {product.stock} -> {product.stock - cart_item.quantity}")
                product.stock -= cart_item.quantity
                product.save()
            
            # Clear cart
            print("Clearing cart...")
            cart.delete()
            
            if order.payment_method == 'zalopay':
                print("Redirecting to ZaloPay payment...")
                return redirect('create_zalopay_order')
            
            messages.success(request, 'Đặt hàng thành công! Cảm ơn bạn đã mua hàng.')
            return redirect('order_success', order_id=order.id)
            
        except Exception as e:
            print("Error during checkout:", str(e))
            print("Error type:", type(e))
            import traceback
            print("Full traceback:")
            traceback.print_exc()
            messages.error(request, f'Có lỗi xảy ra khi đặt hàng: {str(e)}')
            return redirect('checkout')
    
    context = {
        'cart_items': cart.items.all(),
        'subtotal': cart.get_subtotal(),
        'shipping_fee': cart.get_shipping_fee(),
        'discount': cart.get_discount(),
        'total': cart.get_total(),
        'cart_count': get_cart_count(request)
    }
    return render(request, 'core/checkout.html', context)

@login_required
def order_success(request, order_id):
    order = get_object_or_404(Order, id=order_id, user=request.user)
    context = {
        'order': order
    }
    return render(request, 'core/order_success.html', context)

@login_required
def my_orders(request):
    orders = Order.objects.filter(user=request.user).order_by('-created_at')
    context = {
        'orders': orders,
        'cart_count': get_cart_count(request)
    }
    return render(request, 'core/my_orders.html', context)

@login_required
def order_detail(request, order_id):
    order = get_object_or_404(Order, id=order_id, user=request.user)
    context = {
        'order': order,
        'cart_count': get_cart_count(request)
    }
    return render(request, 'core/order_detail.html', context)

@login_required
@require_POST
def cancel_order(request, order_id):
    order = get_object_or_404(Order, id=order_id, user=request.user)
    
    # Chỉ cho phép hủy đơn hàng khi trạng thái là "Chờ xác nhận"
    if order.status != 'pending':
        messages.error(request, 'Không thể hủy đơn hàng này vì đã được xử lý')
        return redirect('order_detail', order_id=order.id)
    
    try:
        # Cập nhật trạng thái đơn hàng
        order.status = 'cancelled'
        order.save()
        
        # Hoàn lại số lượng sản phẩm vào kho
        for item in order.items.all():
            product = item.product
            if product:
                product.stock += item.quantity
                product.save()
        
        messages.success(request, 'Đơn hàng đã được hủy thành công')
    except Exception as e:
        messages.error(request, 'Có lỗi xảy ra khi hủy đơn hàng. Vui lòng thử lại sau.')
    
    return redirect('order_detail', order_id=order.id)

def products(request):
    products = Product.objects.filter(is_available=True)
    categories = Category.objects.all()

    # Get current filters
    current_category = request.GET.get('category')
    current_sort = request.GET.get('sort')
    current_price_range = request.GET.get('price')
    search_query = request.GET.get('search')

    # Apply search filter
    if search_query:
        products = products.filter(
            Q(name__icontains=search_query) |
            Q(description__icontains=search_query) |
            Q(category__name__icontains=search_query)
        ).distinct()

    # Apply category filter
    if current_category:
        products = products.filter(category_id=current_category)

    # Apply price range filter
    if current_price_range:
        if current_price_range == '0-5m':
            products = products.filter(price__lt=5000000)
        elif current_price_range == '5m-10m':
            products = products.filter(price__gte=5000000, price__lt=10000000)
        elif current_price_range == '10m-20m':
            products = products.filter(price__gte=10000000, price__lt=20000000)
        elif current_price_range == '20m+':
            products = products.filter(price__gte=20000000)

    # Apply sorting
    if current_sort:
        if current_sort == 'price_asc':
            products = products.order_by('price')
        elif current_sort == 'price_desc':
            products = products.order_by('-price')
        elif current_sort == 'name':
            products = products.order_by('name')
    else:
        products = products.order_by('-created_at')

    # Pagination
    paginator = Paginator(products, 6)  # 6 sản phẩm mỗi trang
    page = request.GET.get('page', 1)
    
    try:
        products = paginator.get_page(page)
    except PageNotAnInteger:
        products = paginator.page(1)
    except EmptyPage:
        products = paginator.page(paginator.num_pages)
    
    context = {
        'products': products,
        'categories': categories,
        'current_category': current_category,
        'current_sort': current_sort,
        'current_price_range': current_price_range,
        'search_query': search_query,
        'cart_count': get_cart_count(request)
    }
    return render(request, 'core/products.html', context)

class PromotionListView(LoginRequiredMixin, ListView):
    model = Promotion
    template_name = 'admin/promotions.html'
    context_object_name = 'promotions'
    paginate_by = 10
    
    def get_queryset(self):
        return Promotion.objects.all().order_by('-created_at')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        today = timezone.now()
        
        # Active promotions count
        context['active_promotions'] = Promotion.objects.filter(
            is_active=True,
            start_date__lte=today,
            end_date__gte=today
        ).count()
        
        # Used today count
        context['used_today'] = Order.objects.filter(
            promotion__isnull=False,
            created_at__date=today.date()
        ).count()
        
        # Total discount amount
        context['total_discount'] = Order.objects.filter(
            promotion__isnull=False,
            created_at__date=today.date()
        ).aggregate(
            total=Sum('discount')
        )['total'] or 0
        
        # Promotions expiring in next 7 days
        context['expire_soon'] = Promotion.objects.filter(
            is_active=True,
            end_date__range=[today, today + timedelta(days=7)]
        ).count()
        
        return context

@require_POST
def admin_promotion_create(request):
    try:
        data = json.loads(request.body)
        
        # Convert dates to timezone-aware
        start_date = timezone.make_aware(datetime.strptime(data['start_date'], '%Y-%m-%d'))
        end_date = timezone.make_aware(datetime.strptime(data['end_date'], '%Y-%m-%d'))
        
        promotion = Promotion.objects.create(
            code=data['code'],
            name=data['name'],
            description=data.get('description', ''),
            discount_type=data['discount_type'],
            discount_value=data['discount_value'],
            start_date=start_date,
            end_date=end_date,
            usage_limit=data.get('usage_limit'),
            min_order_value=data.get('min_order_value', 0),
            max_discount=data.get('max_discount'),
            is_active=data.get('is_active', True)
        )
        
        return JsonResponse({
            'success': True,
            'message': 'Tạo khuyến mãi thành công',
            'promotion': {
                'id': promotion.id,
                'code': promotion.code,
                'name': promotion.name,
                'discount_type': promotion.get_discount_type_display(),
                'discount_value': promotion.discount_value,
                'end_date': promotion.end_date.strftime('%d/%m/%Y'),
                'is_valid': promotion.is_valid()
            }
        })
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': str(e)
        }, status=400)

@require_POST
def admin_promotion_update(request, promotion_id):
    try:
        promotion = get_object_or_404(Promotion, id=promotion_id)
        data = json.loads(request.body)
        
        # Convert dates to timezone-aware
        start_date = timezone.make_aware(datetime.strptime(data['start_date'], '%Y-%m-%d'))
        end_date = timezone.make_aware(datetime.strptime(data['end_date'], '%Y-%m-%d'))
        
        promotion.code = data['code']
        promotion.name = data['name']
        promotion.description = data.get('description', '')
        promotion.discount_type = data['discount_type']
        promotion.discount_value = data['discount_value']
        promotion.start_date = start_date
        promotion.end_date = end_date
        promotion.usage_limit = data.get('usage_limit')
        promotion.min_order_value = data.get('min_order_value', 0)
        promotion.max_discount = data.get('max_discount')
        promotion.is_active = data.get('is_active', True)
        promotion.save()
        
        return JsonResponse({
            'success': True,
            'message': 'Cập nhật khuyến mãi thành công',
            'promotion': {
                'id': promotion.id,
                'code': promotion.code,
                'name': promotion.name,
                'discount_type': promotion.get_discount_type_display(),
                'discount_value': promotion.discount_value,
                'end_date': promotion.end_date.strftime('%d/%m/%Y'),
                'is_valid': promotion.is_valid()
            }
        })
    except Promotion.DoesNotExist:
        return JsonResponse({
            'success': False,
            'message': 'Không tìm thấy khuyến mãi'
        }, status=404)
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': str(e)
        }, status=400)

@require_POST
def admin_promotion_delete(request, promotion_id):
    try:
        promotion = get_object_or_404(Promotion, id=promotion_id)
        promotion.delete()
        return JsonResponse({
            'success': True,
            'message': 'Xóa khuyến mãi thành công'
        })
    except Promotion.DoesNotExist:
        return JsonResponse({
            'success': False,
            'message': 'Không tìm thấy khuyến mãi'
        }, status=404)
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': str(e)
        }, status=400)

def admin_promotion_detail(request, promotion_id):
    try:
        promotion = get_object_or_404(Promotion, id=promotion_id)
        return JsonResponse({
            'id': promotion.id,
            'code': promotion.code,
            'name': promotion.name,
            'description': promotion.description,
            'discount_type': promotion.discount_type,
            'discount_value': promotion.discount_value,
            'start_date': promotion.start_date.strftime('%Y-%m-%d'),
            'end_date': promotion.end_date.strftime('%Y-%m-%d'),
            'usage_limit': promotion.usage_limit,
            'min_order_value': promotion.min_order_value,
            'max_discount': promotion.max_discount,
            'is_active': promotion.is_active
        })
    except Promotion.DoesNotExist:
        return JsonResponse({
            'success': False,
            'message': 'Không tìm thấy khuyến mãi'
        }, status=404)

def apply_promotion(request):
    if request.method == 'POST':
        try:
            data = json.loads(request.body)
            code = data.get('code')
            
            # Kiểm tra mã khuyến mãi
            promotion = get_object_or_404(Promotion, code=code)
            
            # Lấy giỏ hàng hiện tại
            cart = Cart.objects.get(user=request.user)
            cart_total = cart.get_total()
            
            # Kiểm tra điều kiện áp dụng
            if not promotion.is_valid():
                return JsonResponse({
                    'status': 'error',
                    'message': 'Mã khuyến mãi đã hết hạn hoặc không còn hiệu lực'
                })
            
            if promotion.min_order_value and cart_total < promotion.min_order_value:
                return JsonResponse({
                    'status': 'error',
                    'message': f'Đơn hàng tối thiểu {promotion.min_order_value:,}đ để áp dụng mã này'
                })
            
            # Tính giảm giá
            if promotion.discount_type == 'percentage':
                discount = cart_total * (promotion.discount_value / 100)
                if promotion.max_discount:
                    discount = min(discount, promotion.max_discount)
            else:
                discount = promotion.discount_value
            
            # Cập nhật giỏ hàng
            cart.promotion = promotion
            cart.discount = discount
            cart.save()
            
            return JsonResponse({
                'status': 'success',
                'message': 'Áp dụng mã giảm giá thành công',
                'discount': discount,
                'total': cart_total - discount
            })
            
        except Promotion.DoesNotExist:
            return JsonResponse({
                'status': 'error',
                'message': 'Mã khuyến mãi không tồn tại'
            })
        except Exception as e:
            return JsonResponse({
                'status': 'error',
                'message': str(e)
            })
    
    return JsonResponse({
        'status': 'error',
        'message': 'Phương thức không được hỗ trợ'
    })

def remove_promotion(request):
    if request.method == 'POST':
        try:
            cart = Cart.objects.get(user=request.user)
            cart.promotion = None
            cart.discount = 0
            cart.save()
            
            return JsonResponse({
                'status': 'success',
                'message': 'Đã xóa mã giảm giá',
                'total': cart.get_total()
            })
        except Exception as e:
            return JsonResponse({
                'status': 'error',
                'message': str(e)
            })
    
    return JsonResponse({
        'status': 'error',
        'message': 'Phương thức không được hỗ trợ'
    })

@staff_member_required
def admin_orders(request):
    # Get filters from request
    status = request.GET.get('status', '')
    payment_status = request.GET.get('payment_status', '')
    search = request.GET.get('search', '')
    
    # Base queryset
    orders = Order.objects.all().order_by('-created_at')
    
    # Apply filters
    if status:
        orders = orders.filter(status=status)
    if payment_status:
        orders = orders.filter(payment_status=payment_status)
    if search:
        orders = orders.filter(
            Q(full_name__icontains=search) |
            Q(email__icontains=search) |
            Q(phone__icontains=search)
        )
    
    # Handle Excel export
    if request.GET.get('export') == 'excel':
        return export_orders_to_excel(orders)
    
    # Pagination
    paginator = Paginator(orders, 10)
    page = request.GET.get('page', 1)
    orders = paginator.get_page(page)
    
    return render(request, 'admin/orders.html', {
        'orders': orders,
        'status_choices': Order.STATUS_CHOICES,
        'payment_status_choices': Order.PAYMENT_STATUS_CHOICES,
        'current_status': status,
        'current_payment_status': payment_status,
        'search': search,
    })

@staff_member_required
def admin_order_detail(request, order_id):
    order = get_object_or_404(Order, id=order_id)
    return render(request, 'admin/order_detail.html', {
        'order': order,
        'status_choices': Order.STATUS_CHOICES,
        'payment_status_choices': Order.PAYMENT_STATUS_CHOICES,
    })

@staff_member_required
def admin_update_order_status(request, order_id):
    if request.method == 'POST':
        order = get_object_or_404(Order, id=order_id)
        data = json.loads(request.body)
        new_status = data.get('status')
        
        if new_status in dict(Order.STATUS_CHOICES):
            order.status = new_status
            order.save()
            return JsonResponse({'success': True})
    
    return JsonResponse({'success': False})

@staff_member_required
def admin_update_payment_status(request, order_id):
    if request.method == 'POST':
        order = get_object_or_404(Order, id=order_id)
        data = json.loads(request.body)
        new_status = data.get('payment_status')
        
        if new_status in dict(Order.PAYMENT_STATUS_CHOICES):
            order.payment_status = new_status
            order.save()
            return JsonResponse({'success': True})
    
    return JsonResponse({'success': False})

@staff_member_required
def admin_export_orders(request):
    orders = Order.objects.all().order_by('-created_at')
    return export_orders_to_excel(orders)

def export_orders_to_excel(orders):
    wb = Workbook()
    ws = wb.active
    ws.title = "Orders"
    
    # Define headers
    headers = [
        'ID', 'Khách hàng', 'Email', 'Số điện thoại', 'Địa chỉ',
        'Tổng tiền', 'Phí ship', 'Giảm giá', 'Thành tiền',
        'Trạng thái', 'Trạng thái thanh toán', 'Ngày tạo'
    ]
    
    # Style headers
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=1, column=col)
        cell.value = header
        cell.font = Font(bold=True)
        cell.alignment = Alignment(horizontal='center')
    
    # Add data
    for row, order in enumerate(orders, 2):
        ws.cell(row=row, column=1, value=order.id)
        ws.cell(row=row, column=2, value=order.full_name)
        ws.cell(row=row, column=3, value=order.email)
        ws.cell(row=row, column=4, value=order.phone)
        ws.cell(row=row, column=5, value=order.address)
        ws.cell(row=row, column=6, value=float(order.subtotal))
        ws.cell(row=row, column=7, value=float(order.shipping_fee))
        ws.cell(row=row, column=8, value=float(order.discount))
        ws.cell(row=row, column=9, value=float(order.total))
        ws.cell(row=row, column=10, value=order.get_status_display())
        ws.cell(row=row, column=11, value=order.get_payment_status_display())
        ws.cell(row=row, column=12, value=order.created_at.strftime('%d/%m/%Y %H:%M'))
    
    # Adjust column widths
    for col in ws.columns:
        max_length = 0
        column = col[0].column_letter
        for cell in col:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        adjusted_width = (max_length + 2)
        ws.column_dimensions[column].width = adjusted_width
    
    # Create response
    response = HttpResponse(
        content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )
    response['Content-Disposition'] = f'attachment; filename=orders_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
    
    wb.save(response)
    return response

@login_required
@staff_member_required
def admin_product_detail_json(request, product_id):
    """API endpoint trả về thông tin chi tiết sản phẩm dạng JSON"""
    product = get_object_or_404(Product, id=product_id)
    
    data = {
        'id': product.id,
        'name': product.name,
        'sku': product.sku,
        'description': product.description,
        'price': f"{product.price:,.0f}",
        'stock': product.stock,
        'category': product.category.name,
        'image': product.image.url if product.image else None,
        'specifications': product.specifications,
        'features': product.features,
        'created_at': product.created_at.strftime("%d/%m/%Y"),
        'updated_at': product.updated_at.strftime("%d/%m/%Y"),
        'status': product.status,
    }
    
    return JsonResponse(data)

@login_required
@staff_member_required
def admin_news(request):
    news_list = News.objects.all()
    
    # Search
    search_query = request.GET.get('search', '')
    if search_query:
        news_list = news_list.filter(
            Q(title__icontains=search_query) |
            Q(summary__icontains=search_query)
        )
    
    # Status filter
    status = request.GET.get('status')
    if status:
        news_list = news_list.filter(status=status)
    
    # Pagination
    paginator = Paginator(news_list, 10)
    page = request.GET.get('page')
    news = paginator.get_page(page)
    
    context = {
        'news': news,
        'search_query': search_query,
        'status': status
    }
    return render(request, 'admin/news.html', context)

@login_required
@staff_member_required
def admin_create_news(request):
    if request.method == 'POST':
        form = NewsForm(request.POST, request.FILES)
        if form.is_valid():
            news = form.save(commit=False)
            news.author = request.user
            news.save()
            messages.success(request, 'Tạo tin tức thành công!')
            return redirect('admin_news')
    else:
        form = NewsForm()
    
    context = {'form': form}
    return render(request, 'admin/news_form.html', context)

@login_required
@staff_member_required
def admin_edit_news(request, news_id):
    news = get_object_or_404(News, id=news_id)
    
    if request.method == 'POST':
        form = NewsForm(request.POST, request.FILES, instance=news)
        if form.is_valid():
            form.save()
            messages.success(request, 'Cập nhật tin tức thành công!')
            return redirect('admin_news')
    else:
        form = NewsForm(instance=news)
    
    context = {
        'form': form,
        'news': news
    }
    return render(request, 'admin/news_form.html', context)

@login_required
@staff_member_required
def admin_delete_news(request, news_id):
    news = get_object_or_404(News, id=news_id)
    if request.method == 'POST':
        news.delete()
        messages.success(request, 'Xóa tin tức thành công!')
        return redirect('admin_news')
    return render(request, 'admin/news_confirm_delete.html', {'news': news})

def news_list(request):
    search_query = request.GET.get('search', '')
    news_queryset = News.objects.filter(status='published').order_by('-created_at')
    
    if search_query:
        news_queryset = news_queryset.filter(
            Q(title__icontains=search_query) |
            Q(summary__icontains=search_query) |
            Q(content__icontains=search_query)
        )
    
    # Get featured news
    featured_news = News.objects.filter(status='published').order_by('-views').first()
    
    # Paginate results
    paginator = Paginator(news_queryset, 3)  # 3 items per page
    page = request.GET.get('page')
    news_list = paginator.get_page(page)
    
    context = {
        'news_list': news_list,
        'featured_news': featured_news,
        'search_query': search_query,
    }
    return render(request, 'core/news.html', context)

def news_detail(request, slug):
    news = get_object_or_404(News, slug=slug, status='published')
    
    # Get related news
    related_news = News.objects.filter(status='published')\
        .exclude(id=news.id)\
        .order_by('-created_at')[:2]
    
    context = {
        'news': news,
        'related_news': related_news,
    }
    return render(request, 'core/news_detail.html', context)

@require_POST
def news_increase_views(request, news_id):
    news = get_object_or_404(News, id=news_id)
    news.views += 1
    news.save()
    return JsonResponse({'status': 'success', 'views': news.views})

@login_required
@staff_member_required
def admin_inventory(request):
    # Get filters from request
    search = request.GET.get('search', '')
    type_filter = request.GET.get('type', '')
    date_from = request.GET.get('date_from', '')
    date_to = request.GET.get('date_to', '')

    # Base queryset
    transactions = InventoryTransaction.objects.all()

    # Apply filters
    if search:
        transactions = transactions.filter(
            Q(transaction_id__icontains=search) |
            Q(product__name__icontains=search) |
            Q(supplier__icontains=search)
        )
    
    if type_filter:
        transactions = transactions.filter(type=type_filter)
    
    if date_from:
        transactions = transactions.filter(created_at__date__gte=date_from)
    
    if date_to:
        transactions = transactions.filter(created_at__date__lte=date_to)

    # Paginate results
    paginator = Paginator(transactions, 10)
    page = request.GET.get('page')
    transactions = paginator.get_page(page)

    # Get summary statistics
    total_import = InventoryTransaction.objects.filter(type='import').aggregate(
        total=Sum('total_price'))['total'] or 0
    total_export = InventoryTransaction.objects.filter(type='export').aggregate(
        total=Sum('total_price'))['total'] or 0

    context = {
        'transactions': transactions,
        'search': search,
        'type_filter': type_filter,
        'date_from': date_from,
        'date_to': date_to,
        'total_import': total_import,
        'total_export': total_export,
    }
    return render(request, 'admin/inventory.html', context)

@login_required
@staff_member_required
def admin_inventory_create(request):
    if request.method == 'POST':
        form = InventoryTransactionForm(request.POST)
        if form.is_valid():
            transaction = form.save(commit=False)
            transaction.created_by = request.user
            transaction.save()
            messages.success(request, 'Tạo giao dịch kho thành công')
            return redirect('admin_inventory')
    else:
        form = InventoryTransactionForm()
    
    context = {
        'form': form,
        'title': 'Tạo giao dịch kho mới'
    }
    return render(request, 'admin/inventory_form.html', context)

@login_required
@staff_member_required
def admin_inventory_detail(request, transaction_id):
    transaction = get_object_or_404(InventoryTransaction, id=transaction_id)
    context = {
        'transaction': transaction
    }
    return render(request, 'admin/inventory_detail.html', context)

@login_required
@staff_member_required
def admin_inventory_delete(request, transaction_id):
    transaction = get_object_or_404(InventoryTransaction, id=transaction_id)
    
    if request.method == 'POST':
        # Reverse the stock update
        if transaction.type == 'import':
            transaction.product.stock -= transaction.quantity
        else:
            transaction.product.stock += transaction.quantity
        transaction.product.save()
        
        transaction.delete()
        messages.success(request, 'Xóa giao dịch kho thành công')
        return redirect('admin_inventory')
    
    context = {
        'transaction': transaction
    }
    return render(request, 'admin/inventory_confirm_delete.html', context)

@login_required
@staff_member_required
def admin_export_inventory(request):
    # Get all inventory transactions
    transactions = InventoryTransaction.objects.select_related('product', 'created_by').all()
    
    # Create Excel workbook
    wb = openpyxl.Workbook()
    ws = wb.active
    ws.title = "Giao dịch kho"
    
    # Define headers
    headers = [
        'Mã giao dịch', 'Loại giao dịch', 'Sản phẩm', 'Số lượng', 
        'Đơn giá', 'Tổng giá trị', 'Nhà cung cấp', 'Ghi chú', 
        'Người tạo', 'Thời gian tạo'
    ]
    
    # Write headers
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=1, column=col)
        cell.value = header
        cell.font = openpyxl.styles.Font(bold=True)
        cell.fill = openpyxl.styles.PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
        cell.border = openpyxl.styles.Border(
            left=openpyxl.styles.Side(style='thin'),
            right=openpyxl.styles.Side(style='thin'),
            top=openpyxl.styles.Side(style='thin'),
            bottom=openpyxl.styles.Side(style='thin')
        )
    
    # Write data
    for row, transaction in enumerate(transactions, 2):
        ws.cell(row=row, column=1).value = transaction.id
        ws.cell(row=row, column=2).value = transaction.get_type_display()
        ws.cell(row=row, column=3).value = transaction.product.name
        ws.cell(row=row, column=4).value = transaction.quantity
        ws.cell(row=row, column=5).value = transaction.unit_price
        ws.cell(row=row, column=6).value = transaction.total_price
        ws.cell(row=row, column=7).value = transaction.supplier or ''
        ws.cell(row=row, column=8).value = transaction.note or ''
        ws.cell(row=row, column=9).value = transaction.created_by.get_full_name() or transaction.created_by.username
        ws.cell(row=row, column=10).value = transaction.created_at.strftime("%d/%m/%Y %H:%M")
        
        # Format currency cells
        for col in [5, 6]:  # Đơn giá và Tổng giá trị
            cell = ws.cell(row=row, column=col)
            cell.number_format = '#,##0'
    
    # Adjust column widths
    for col in ws.columns:
        max_length = 0
        column = col[0].column_letter
        for cell in col:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        adjusted_width = (max_length + 2)
        ws.column_dimensions[column].width = adjusted_width
    
    # Create response
    response = HttpResponse(content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
    response['Content-Disposition'] = 'attachment; filename="inventory_transactions.xlsx"'
    
    # Save workbook
    wb.save(response)
    
    return response

@login_required
@staff_member_required
def admin_users(request):
    # Get filters from request
    search = request.GET.get('search', '')
    role = request.GET.get('role', '')
    status = request.GET.get('status', '')
    
    # Base queryset
    users = User.objects.all().order_by('-date_joined')
    
    # Apply filters
    if search:
        users = users.filter(
            Q(username__icontains=search) |
            Q(email__icontains=search) |
            Q(first_name__icontains=search) |
            Q(last_name__icontains=search)
        )
    
    if role:
        if role == 'admin':
            users = users.filter(is_superuser=True)
        elif role == 'staff':
            users = users.filter(is_staff=True, is_superuser=False)
        elif role == 'user':
            users = users.filter(is_staff=False, is_superuser=False)
    
    if status:
        if status == 'active':
            users = users.filter(is_active=True)
        elif status == 'inactive':
            users = users.filter(is_active=False)
    
    # Pagination
    paginator = Paginator(users, 10)
    page = request.GET.get('page')
    users = paginator.get_page(page)
    
    context = {
        'users': users,
        'search': search,
        'role': role,
        'status': status
    }
    return render(request, 'admin/users.html', context)

@login_required
@staff_member_required
def admin_user_detail(request, user_id):
    try:
        user = User.objects.get(id=user_id)
        orders_count = Order.objects.filter(user=user).count()
        
        return JsonResponse({
            'id': user.id,
            'username': user.username,
            'email': user.email,
            'full_name': f"{user.first_name} {user.last_name}".strip() or None,
            'phone': user.phone if hasattr(user, 'phone') else None,
            'is_superuser': user.is_superuser,
            'is_staff': user.is_staff,
            'is_active': user.is_active,
            'date_joined': user.date_joined.strftime("%d/%m/%Y %H:%M"),
            'orders_count': orders_count
        })
    except User.DoesNotExist:
        return JsonResponse({
            'success': False,
            'message': 'Không tìm thấy người dùng'
        }, status=404)

@login_required
@staff_member_required
def admin_user_toggle_status(request, user_id):
    if request.method == 'POST':
        try:
            user = User.objects.get(id=user_id)
            
            # Prevent deactivating superuser
            if user.is_superuser and not user.is_active:
                return JsonResponse({
                    'success': False,
                    'message': 'Không thể khóa tài khoản quản trị viên'
                })
            
            user.is_active = not user.is_active
            user.save()
            
            return JsonResponse({
                'success': True,
                'message': 'Cập nhật trạng thái thành công'
            })
        except User.DoesNotExist:
            return JsonResponse({
                'success': False,
                'message': 'Không tìm thấy người dùng'
            }, status=404)
    
    return JsonResponse({
        'success': False,
        'message': 'Phương thức không được hỗ trợ'
    }, status=405)

@login_required
def chat_room(request, room_id=None):
    if room_id:
        room = get_object_or_404(ChatRoom, id=room_id)
        if not request.user.is_staff and request.user not in room.participants.all():
            return redirect('chat_room')
    else:
        room = ChatRoom.objects.filter(participants=request.user).first()
        if not room:
            room = ChatRoom.objects.create(name=f"Chat với {request.user.username}")
            room.participants.add(request.user)

    # Get all messages for this room
    messages = ChatMessage.objects.filter(room=room)
    
    # Mark unread messages as read
    if not request.user.is_staff:
        messages.filter(sender__is_staff=True, is_read=False).update(is_read=True)
    else:
        messages.filter(sender__in=room.participants.all(), is_read=False).exclude(sender=request.user).update(is_read=True)

    context = {
        'room': room,
        'messages': messages,
        'user_id': request.user.id
    }
    
    if request.user.is_staff:
        # For admin, show all chat rooms
        context['chat_rooms'] = ChatRoom.objects.all().order_by('-updated_at')
        return render(request, 'admin/chat.html', context)
    else:
        # For regular users
        return render(request, 'core/chat.html', context)

@login_required
@staff_member_required
def admin_chat_list(request):
    if not request.user.is_staff:
        return redirect('home')
        
    # Get all chat rooms
    chat_rooms = ChatRoom.objects.all()
    
    # Add unread count for each room
    for room in chat_rooms:
        room.unread_count = room.get_unread_count(request.user)
    
    # Get current room if specified
    current_room = None
    room_id = request.GET.get('room')
    if room_id:
        try:
            current_room = ChatRoom.objects.get(id=room_id)
            # Mark messages as read when viewing the room
            current_room.messages.filter(is_read=False).exclude(sender=request.user).update(is_read=True)
        except ChatRoom.DoesNotExist:
            pass
    
    context = {
        'chat_rooms': chat_rooms,
        'current_room': current_room,
    }
    return render(request, 'admin/chat_list.html', context)

@login_required
def admin_chat_room(request, room_id):
    if not request.user.is_staff:
        return redirect('home')
        
    room = get_object_or_404(ChatRoom, id=room_id)
    messages = ChatMessage.objects.filter(room=room).order_by('created_at')
    
    # Mark messages as read
    messages.filter(is_read=False).exclude(sender=request.user).update(is_read=True)
    
    # Get all chat rooms for sidebar
    chat_rooms = ChatRoom.objects.all().order_by('-updated_at')
    
    context = {
        'current_room': room,
        'messages': messages,
        'chat_rooms': chat_rooms,
        'user_id': request.user.id
    }
    
    return render(request, 'admin/chat.html', context)

@login_required
def chat_unread_count(request):
    if request.user.is_staff:
        # For admin, count all unread messages from non-staff users
        count = ChatMessage.objects.filter(
            is_read=False,
            sender__is_staff=False
        ).count()
    else:
        # For regular users, count unread messages from admin in their chat room
        try:
            room = ChatRoom.objects.get(participants=request.user)
            count = room.messages.filter(
                is_read=False,
                sender__is_staff=True
            ).count()
        except ChatRoom.DoesNotExist:
            count = 0
    
    return JsonResponse({'count': count})

@login_required
def create_chat_room(request):
    if request.method == 'POST':
        try:
            # Check if user already has a chat room
            existing_room = ChatRoom.objects.filter(participants=request.user).first()
            if existing_room:
                return JsonResponse({
                    'success': True,
                    'room_id': existing_room.id,
                    'message': 'Bạn đã có phòng chat'
                })
            
            # Create new chat room
            room = ChatRoom.objects.create(name=f"Chat với {request.user.username}")
            room.participants.add(request.user)
            
            return JsonResponse({
                'success': True,
                'room_id': room.id,
                'message': 'Tạo phòng chat thành công'
            })
        except Exception as e:
            return JsonResponse({
                'success': False,
                'message': str(e)
            }, status=400)
    
    return JsonResponse({
        'success': False,
        'message': 'Phương thức không được hỗ trợ'
    }, status=405)

@csrf_exempt
def chatbot_api(request):
    if request.method == 'POST':
        try:
            data = json.loads(request.body)
            user_message = data.get('message', '').strip()
            
            if not user_message:
                return JsonResponse({
                    'success': False,
                    'message': 'Tin nhắn không được để trống'
                })
            
            # Lấy thông tin sản phẩm và danh mục để chatbot có context
            products_info = []
            categories_info = []
            
            # Lấy tất cả danh mục
            categories = Category.objects.all()
            for category in categories:
                category_info = {
                    'name': category.name,
                    'description': category.description if category.description else '',
                    'product_count': Product.objects.filter(category=category, is_available=True).count()
                }
                categories_info.append(category_info)
            
            # Lấy tất cả sản phẩm có sẵn
            products = Product.objects.filter(is_available=True).select_related('category')
            
            for product in products:
                # Lấy hình ảnh đầu tiên của sản phẩm
                first_image = ProductImage.objects.filter(product=product).first()
                
                product_info = {
                    'id': product.id,
                    'name': product.name,
                    'price': f"{product.price:,.0f} VNĐ",
                    'price_number': int(product.price),
                    'category': product.category.name,
                    'description': product.description,
                    'stock': product.stock,
                    'specifications': product.specifications if product.specifications else {},
                    'image_url': first_image.image.url if first_image else None,
                    'status': 'Còn hàng' if product.stock > 0 else 'Hết hàng'
                }
                products_info.append(product_info)
            
            # Tạo system prompt cho chatbot
            system_prompt = f"""
            Bạn là trợ lý ảo của Fashion Elite - thương hiệu thời trang trẻ trung và hiện đại hàng đầu Việt Nam. 

            DANH MỤC SẢN PHẨM CÓ SẴN:
            {json.dumps(categories_info, ensure_ascii=False, indent=2)}

            SẢN PHẨM THỜI TRANG HIỆN CÓ TRONG KHO:
            {json.dumps(products_info, ensure_ascii=False, indent=2)}

            HƯỚNG DẪN TƯ VẤN:
            1. CHỈ tư vấn về các sản phẩm có trong danh sách trên
            2. Khi khách hỏi về sản phẩm, hãy kiểm tra kho hàng (stock) và trạng thái
            3. Đưa ra giá cả chính xác từ database
            4. Gợi ý sản phẩm cụ thể với tên và ID
            5. Tư vấn phối đồ dựa trên sản phẩm có sẵn
            6. Không bịa đặt sản phẩm không có trong danh sách
            7. Nếu hết hàng, gợi ý sản phẩm tương tự còn hàng
            8. Sử dụng emoji thời trang phù hợp: ✨👗💫👠🛍️

            PHONG CÁCH TRẢ LỜI:
            - Thân thiện, trendy như fashion stylist chuyên nghiệp
            - Trả lời bằng tiếng Việt, giọng điệu trẻ trung
            - Chỉ trả lời về thời trang và sản phẩm Fashion Elite
            - Từ chối lịch sự nếu hỏi chủ đề khác
            """
            
            # Kiểm tra API key
            api_key = settings.OPENAI_API_KEY
            if not api_key:
                return JsonResponse({
                    'success': False,
                    'message': 'OpenAI API key chưa được cấu hình'
                })
            
            print(f"Calling OpenAI API with message: {user_message}")
            
            # Gọi OpenAI API bằng requests
            openai_url = "https://api.openai.com/v1/chat/completions"
            headers = {
                "Authorization": f"Bearer {api_key}",
                "Content-Type": "application/json"
            }
            
            payload = {
                "model": "gpt-3.5-turbo",
                "messages": [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_message}
                ],
                "max_tokens": 500,
                "temperature": 0.7
            }
            
            response = requests.post(openai_url, headers=headers, json=payload, timeout=30)
            
            if response.status_code == 200:
                response_data = response.json()
                bot_message = response_data['choices'][0]['message']['content'].strip()
                print(f"OpenAI response: {bot_message}")
                
                return JsonResponse({
                    'success': True,
                    'message': bot_message
                })
            elif response.status_code == 401:
                return JsonResponse({
                    'success': False,
                    'message': 'API key không hợp lệ. Vui lòng kiểm tra lại.'
                })
            elif response.status_code == 429:
                return JsonResponse({
                    'success': False,
                    'message': 'API đã đạt giới hạn. Vui lòng thử lại sau.'
                })
            else:
                return JsonResponse({
                    'success': False,
                    'message': f'Lỗi API: {response.status_code} - {response.text}'
                })
            
        except requests.exceptions.Timeout:
            return JsonResponse({
                'success': False,
                'message': 'Timeout khi gọi API. Vui lòng thử lại.'
            })
        except requests.exceptions.RequestException as e:
            return JsonResponse({
                'success': False,
                'message': f'Lỗi kết nối: {str(e)}'
            })
        except Exception as e:
            return JsonResponse({
                'success': False,
                'message': f'Có lỗi xảy ra: {str(e)}'
            })
    
    return JsonResponse({
        'success': False,
        'message': 'Phương thức không được hỗ trợ'
    }, status=405)

def chatbot_view(request):
    context = {
        'cart_count': get_cart_count(request)
    }
    return render(request, 'core/chatbot.html', context)

@csrf_exempt
def test_chatbot_api(request):
    """Test endpoint để kiểm tra API hoạt động"""
    if request.method == 'POST':
        try:
            data = json.loads(request.body)
            user_message = data.get('message', '').strip()
            
            # Trả về response giả để test
            return JsonResponse({
                'success': True,
                'message': f'Đã nhận tin nhắn: "{user_message}". API hoạt động bình thường! ✨'
            })
            
        except Exception as e:
            return JsonResponse({
                'success': False,
                'message': f'Lỗi server: {str(e)}'
            })
    
    return JsonResponse({
        'success': False,
        'message': 'Chỉ chấp nhận POST request'
    })

# User Review Management Views
@login_required
def my_reviews(request):
    reviews = Review.objects.filter(user=request.user).select_related('product').order_by('-created_at')
    paginator = Paginator(reviews, 10)
    page = request.GET.get('page')
    
    try:
        reviews = paginator.page(page)
    except PageNotAnInteger:
        reviews = paginator.page(1)
    except EmptyPage:
        reviews = paginator.page(paginator.num_pages)
    
    context = {
        'reviews': reviews,
        'cart_count': get_cart_count(request)
    }
    return render(request, 'core/my_reviews.html', context)

@login_required
def edit_review(request, review_id):
    review = get_object_or_404(Review, id=review_id, user=request.user)
    
    if request.method == 'POST':
        form = ReviewForm(request.POST, instance=review)
        if form.is_valid():
            form.save()
            messages.success(request, 'Đánh giá của bạn đã được cập nhật thành công!')
            return redirect('my_reviews')
    else:
        form = ReviewForm(instance=review)
    
    context = {
        'form': form,
        'review': review,
        'cart_count': get_cart_count(request)
    }
    return render(request, 'core/edit_review.html', context)

@login_required
def delete_review(request, review_id):
    review = get_object_or_404(Review, id=review_id, user=request.user)
    
    if request.method == 'POST':
        product_name = review.product.name
        review.delete()
        messages.success(request, f'Đánh giá cho sản phẩm "{product_name}" đã được xóa thành công!')
        return redirect('my_reviews')
    
    context = {
        'review': review,
        'cart_count': get_cart_count(request)
    }
    return render(request, 'core/delete_review.html', context)
