{% load static %}
<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token }}">
    <title>Fashion Elite - {% block title %}Trang chủ{% endblock %}</title>
    <script src="https://cdn.tailwindcss.com"></script>
    {% block extra_css %}{% endblock %}
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="bg-white shadow-sm">
        <nav class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex">
                    <!-- Logo -->
                    <div class="flex-shrink-0 flex items-center">
                        <a href="{% url 'home' %}" class="text-2xl font-bold text-blue-600">Fashion Elite</a>
                    </div>
                    <!-- Navigation Links -->
                    <div class="hidden sm:ml-6 sm:flex sm:space-x-8">
                        <a href="{% url 'home' %}" class="{% if request.path == '/' %}border-blue-500 text-gray-900{% else %}border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700{% endif %} inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            Trang chủ
                        </a>
                        <a href="{% url 'products' %}" class="{% if request.path == '/products/' %}border-blue-500 text-gray-900{% else %}border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700{% endif %} inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            Sản phẩm
                        </a>
                        <a href="{% url 'news' %}" class="{% if request.path == '/news/' %}border-blue-500 text-gray-900{% else %}border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700{% endif %} inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            Tin tức
                        </a>
                        <a href="{% url 'about' %}" class="{% if request.path == '/about/' %}border-blue-500 text-gray-900{% else %}border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700{% endif %} inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            Giới thiệu
                        </a>
                        <a href="{% url 'contact' %}" class="{% if request.path == '/contact/' %}border-blue-500 text-gray-900{% else %}border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700{% endif %} inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            Liên hệ
                        </a>
                        <a href="{% url 'chatbot' %}" class="{% if request.path == '/chatbot/' %}border-blue-500 text-gray-900{% else %}border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700{% endif %} inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            🤖 Chatbot
                        </a>
                    </div>
                </div>
                <!-- Search -->
                <div class="flex-1 flex items-center justify-center px-2 lg:ml-6 lg:justify-end">
                    <div class="max-w-lg w-full lg:max-w-xs">
                        <form action="{% url 'products' %}" method="GET">
                            <label for="search" class="sr-only">Tìm kiếm</label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <svg class="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                        <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                                <input id="search" name="search" value="{{ request.GET.search|default:'' }}" 
                                       class="block w-full pl-10 pr-12 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm" 
                                       placeholder="Tìm kiếm sản phẩm..." 
                                       type="search"
                                       autocomplete="off">
                                <!-- Camera icon for visual search -->
                                <button type="button" onclick="openVisualSearchModal()" 
                                        class="absolute inset-y-0 right-0 pr-3 flex items-center hover:text-blue-600 transition-colors duration-200"
                                        title="Tìm kiếm bằng hình ảnh">
                                    <svg class="h-5 w-5 text-gray-400 hover:text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"/>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 13a3 3 0 11-6 0 3 3 0 016 0z"/>
                                    </svg>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
                <!-- User Menu -->
                <div class="hidden sm:ml-6 sm:flex sm:items-center space-x-4">
                    <a href="{% url 'cart' %}" class="relative text-gray-500 hover:text-gray-700 p-2 group">
                        <span class="sr-only">Giỏ hàng</span>
                        <svg class="h-6 w-6 transition-colors duration-200 group-hover:text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
                        </svg>
                        {% if cart_count > 0 %}
                        <span class="cart-count absolute -top-1 -right-1 bg-red-500 text-white text-xs font-bold rounded-full h-5 w-5 flex items-center justify-center">
                            {{ cart_count }}
                        </span>
                        {% endif %}
                    </a>
                    {% if user.is_authenticated %}
                        <div class="relative" x-data="{ open: false }">
                            <button @click="open = !open" class="flex items-center space-x-2 text-gray-500 hover:text-gray-700">
                                <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                </svg>
                                <span>{{ user.username }}</span>
                            </button>
                            <div x-show="open" @click.away="open = false" class="absolute right-0 mt-2 w-48 rounded-md shadow-lg py-1 bg-white ring-1 ring-black ring-opacity-5 z-50">
                                {% if user.is_staff %}
                                    <a href="{% url 'admin_dashboard' %}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Quản trị hệ thống</a>
                                {% endif %}
                                <a href="{% url 'profile' %}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Thông tin tài khoản</a>
                                <a href="{% url 'my_orders' %}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Đơn hàng của tôi</a>
                                <a href="{% url 'my_reviews' %}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Quản lý đánh giá</a>
                                <a href="{% url 'logout' %}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Đăng xuất</a>
                            </div>
                        </div>
                    {% else %}
                        <a href="{% url 'login' %}" class="text-gray-500 hover:text-gray-700">Đăng nhập</a>
                        <a href="{% url 'register' %}" class="bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600">Đăng ký</a>
                    {% endif %}
                </div>
            </div>
        </nav>
    </header>

    <!-- Messages -->
    {% if messages %}
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mt-4">
            {% for message in messages %}
                <div class="p-4 {% if message.tags == 'error' %}bg-red-100 text-red-700{% else %}bg-green-100 text-green-700{% endif %} rounded-lg">
                    {{ message }}
                </div>
            {% endfor %}
        </div>
    {% endif %}

    <!-- Main Content -->
    <main>
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="bg-white">
        <div class="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                    <h3 class="text-sm font-semibold text-gray-400 tracking-wider uppercase">Về chúng tôi</h3>
                    <ul class="mt-4 space-y-4">
                        <li>
                            <a href="{% url 'about' %}" class="text-base text-gray-500 hover:text-gray-900">
                                Giới thiệu
                            </a>
                        </li>
                        <li>
                            <a href="{% url 'news' %}" class="text-base text-gray-500 hover:text-gray-900">
                                Tin tức
                            </a>
                        </li>
                        <li>
                            <a href="#" class="text-base text-gray-500 hover:text-gray-900">
                                Tuyển dụng
                            </a>
                        </li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-sm font-semibold text-gray-400 tracking-wider uppercase">Hỗ trợ khách hàng</h3>
                    <ul class="mt-4 space-y-4">
                        <li>
                            <a href="#" class="text-base text-gray-500 hover:text-gray-900">
                                Hướng dẫn mua hàng
                            </a>
                        </li>
                        <li>
                            <a href="#" class="text-base text-gray-500 hover:text-gray-900">
                                Chính sách đổi trả
                            </a>
                        </li>
                        <li>
                            <a href="#" class="text-base text-gray-500 hover:text-gray-900">
                                Chính sách bảo hành
                            </a>
                        </li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-sm font-semibold text-gray-400 tracking-wider uppercase">Thanh toán</h3>
                    <ul class="mt-4 space-y-4">
                        <li>
                            <a href="#" class="text-base text-gray-500 hover:text-gray-900">
                                Hình thức thanh toán
                            </a>
                        </li>
                        <li>
                            <a href="#" class="text-base text-gray-500 hover:text-gray-900">
                                Chính sách trả góp
                            </a>
                        </li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-sm font-semibold text-gray-400 tracking-wider uppercase">Liên hệ</h3>
                    <ul class="mt-4 space-y-4">
                        <li class="flex">
                            <svg class="h-6 w-6 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                            </svg>
                            <a href="tel:19001234" class="ml-3 text-base text-gray-500 hover:text-gray-900">
                                1900 1234
                            </a>
                        </li>
                        <li class="flex">
                            <svg class="h-6 w-6 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                            </svg>
                            <a href="mailto:<EMAIL>" class="ml-3 text-base text-gray-500 hover:text-gray-900">
                                <EMAIL>
                            </a>
                        </li>
                        <li>
                            <a href="{% url 'contact' %}" class="text-base text-gray-500 hover:text-gray-900">
                                Liên hệ với chúng tôi
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
            <div class="mt-8 border-t border-gray-200 pt-8">
                <p class="text-base text-gray-400 text-center">
                    &copy; 2025 Fashion Elite. All rights reserved.
                </p>
            </div>
        </div>
    </footer>

    <!-- Toast Notification Container -->
    <div id="toast-container" class="fixed bottom-4 right-4 z-50"></div>

    <!-- Visual Search Modal -->
    <div id="visualSearchModal" class="fixed inset-0 z-50 hidden overflow-y-auto">
        <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div class="fixed inset-0 transition-opacity" aria-hidden="true">
                <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>
            <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
            
            <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                <!-- Modal Header -->
                <div class="bg-gradient-to-r from-blue-500 to-purple-600 px-6 py-4">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <svg class="w-6 h-6 text-white mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"/>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 13a3 3 0 11-6 0 3 3 0 016 0z"/>
                            </svg>
                            <h3 class="text-lg font-semibold text-white">Tìm kiếm bằng hình ảnh</h3>
                        </div>
                        <button onclick="closeVisualSearchModal()" class="text-white hover:text-gray-200 transition-colors duration-200">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                            </svg>
                        </button>
                    </div>
                </div>

                <!-- Modal Content -->
                <div class="bg-white px-6 py-6">
                    {% csrf_token %}
                    <div class="text-center">
                        <!-- Upload Area -->
                        <div id="uploadArea" class="border-2 border-dashed border-gray-300 rounded-lg p-8 mb-6 hover:border-blue-400 transition-colors duration-300 cursor-pointer">
                            <div class="flex flex-col items-center">
                                <svg class="w-16 h-16 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"/>
                                </svg>
                                <h4 class="text-lg font-medium text-gray-900 mb-2">Tải ảnh lên để tìm kiếm</h4>
                                <p class="text-sm text-gray-500 mb-4">Kéo thả ảnh vào đây hoặc click để chọn file</p>
                                <input type="file" id="imageUpload" accept="image/*" class="hidden">
                                <button onclick="document.getElementById('imageUpload').click()" 
                                        class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                                    </svg>
                                    Chọn ảnh
                                </button>
                            </div>
                        </div>

                        <!-- Image Preview -->
                        <div id="imagePreview" class="hidden mb-6">
                            <div class="relative inline-block">
                                <img id="previewImage" src="" alt="Preview" class="max-w-full h-40 object-contain rounded-lg border border-gray-200">
                                <button onclick="removeImage()" class="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center hover:bg-red-600 transition-colors duration-200">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                                    </svg>
                                </button>
                            </div>
                        </div>

                        <!-- Alternative Options -->
                        <div class="grid grid-cols-2 gap-4 mb-6">
                            <button onclick="openCamera()" class="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:border-blue-400 hover:bg-blue-50 transition-all duration-200">
                                <svg class="w-8 h-8 text-gray-600 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"/>
                                </svg>
                                <span class="text-sm font-medium text-gray-700">Chụp ảnh</span>
                            </button>
                            <button onclick="pasteFromClipboard()" class="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:border-blue-400 hover:bg-blue-50 transition-all duration-200">
                                <svg class="w-8 h-8 text-gray-600 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                                </svg>
                                <span class="text-sm font-medium text-gray-700">Dán từ clipboard</span>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Modal Footer -->
                <div class="bg-gray-50 px-6 py-4 flex justify-end space-x-3">
                    <button onclick="closeVisualSearchModal()" 
                            class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        Hủy
                    </button>
                    <button onclick="searchByImage()" 
                            class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                            id="searchButton" disabled>
                        <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                        </svg>
                        Tìm kiếm
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Floating Chatbot - Only show on non-chatbot pages -->
    {% if request.resolver_match.url_name != 'chatbot' %}
        {% include 'partials/floating_chatbot.html' %}
    {% endif %}

    <!-- Chat Button -->
    {% if user.is_authenticated and not user.is_staff %}
    <div class="fixed bottom-6 left-6">
        <a href="{% url 'chat_room' %}" 
           class="flex items-center justify-center w-14 h-14 bg-green-600 rounded-full shadow-lg hover:bg-green-700 transition-colors duration-200">
            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z"/>
            </svg>
            <span id="unread-chat-badge" class="hidden absolute -top-1 -right-1 bg-red-500 text-white text-xs w-5 h-5 flex items-center justify-center rounded-full">0</span>
        </a>
    </div>
    {% endif %}

    <!-- Chat Notification Script -->
    {% if user.is_authenticated and not user.is_staff %}
    <script>
        function updateUnreadCount() {
            fetch('/chat/unread-count/')
                .then(response => response.json())
                .then(data => {
                    const badge = document.getElementById('unread-chat-badge');
                    if (data.count > 0) {
                        badge.textContent = data.count;
                        badge.classList.remove('hidden');
                    } else {
                        badge.classList.add('hidden');
                    }
                });
        }

        // Update unread count every 30 seconds
        setInterval(updateUnreadCount, 30000);
        // Initial update
        updateUnreadCount();
    </script>
    {% endif %}

    <!-- AlpineJS for dropdown -->
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    
    {% block scripts %}{% endblock %}
    {% block extra_js %}{% endblock %}
    
    <script>
    function showToast(message, type = 'success') {
        const toast = document.createElement('div');
        toast.className = `flex items-center p-4 mb-4 rounded-lg shadow-lg ${
            type === 'success' ? 'bg-green-500' : 'bg-red-500'
        } text-white transform transition-all duration-300 translate-y-full`;
        
        toast.innerHTML = `
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                ${type === 'success' 
                    ? '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>'
                    : '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>'}
            </svg>
            <span class="flex-1">${message}</span>
        `;

        const container = document.getElementById('toast-container');
        container.appendChild(toast);

        // Animate in
        setTimeout(() => {
            toast.style.transform = 'translateY(0)';
        }, 100);

        // Remove after 3 seconds
        setTimeout(() => {
            toast.style.transform = 'translateY(full)';
            setTimeout(() => {
                toast.remove();
            }, 300);
        }, 3000);
    }

    // Visual Search Modal Functions
    function openVisualSearchModal() {
        document.getElementById('visualSearchModal').classList.remove('hidden');
    }

    function closeVisualSearchModal() {
        document.getElementById('visualSearchModal').classList.add('hidden');
        // Reset the modal state
        document.getElementById('uploadArea').style.display = 'block';
        document.getElementById('imagePreview').classList.add('hidden');
        document.getElementById('searchButton').disabled = true;
        document.getElementById('previewImage').src = '';
    }

    function openCamera() {
        // For now, just trigger file input with camera preference
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = 'image/*';
        input.capture = 'camera';
        input.onchange = function(e) {
            if (e.target.files[0]) {
                handleImageUpload(e.target.files[0]);
            }
        };
        input.click();
    }

    function pasteFromClipboard() {
        navigator.clipboard.read().then(clipboardItems => {
            for (const clipboardItem of clipboardItems) {
                for (const type of clipboardItem.types) {
                    if (type.startsWith('image/')) {
                        clipboardItem.getType(type).then(blob => {
                            handleImageUpload(blob);
                        });
                        return;
                    }
                }
            }
            alert('Không tìm thấy hình ảnh trong clipboard');
        }).catch(err => {
            alert('Không thể truy cập clipboard: ' + err.message);
        });
    }

    function handleImageUpload(file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            document.getElementById('previewImage').src = e.target.result;
            document.getElementById('uploadArea').style.display = 'none';
            document.getElementById('imagePreview').classList.remove('hidden');
            document.getElementById('searchButton').disabled = false;
        };
        reader.readAsDataURL(file);
    }

    function removeImage() {
        document.getElementById('uploadArea').style.display = 'block';
        document.getElementById('imagePreview').classList.add('hidden');
        document.getElementById('searchButton').disabled = true;
        document.getElementById('previewImage').src = '';
    }

    function searchByImage() {
        const imageData = document.getElementById('previewImage').src;
        console.log('DEBUG: Image data:', imageData);

        if (!imageData) {
            alert('Vui lòng chọn hình ảnh để tìm kiếm');
            return;
        }

        // Show loading state
        const searchButton = document.getElementById('searchButton');
        const originalText = searchButton.innerHTML;
        searchButton.innerHTML = '<svg class="animate-spin w-4 h-4 inline mr-2" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>Đang xử lý...';
        searchButton.disabled = true;

        // Convert base64 to blob and send via fetch
        console.log('DEBUG: Starting fetch for image data');
        fetch(imageData)
            .then(res => {
                console.log('DEBUG: Fetch response:', res);
                return res.blob();
            })
            .then(blob => {
                console.log('DEBUG: Blob created:', blob);
                console.log('DEBUG: Blob size:', blob.size);
                console.log('DEBUG: Blob type:', blob.type);

                const formData = new FormData();
                formData.append('image', blob, 'search_image.jpg');

                // Get CSRF token
                const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]')?.value ||
                                 document.querySelector('meta[name=csrf-token]')?.getAttribute('content');

                console.log('DEBUG: CSRF token:', csrfToken);

                if (csrfToken) {
                    formData.append('csrfmiddlewaretoken', csrfToken);
                }

                console.log('DEBUG: FormData created, sending POST request');

                // Send POST request
                return fetch('/search-by-image/', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-CSRFToken': csrfToken
                    }
                });
            })
            .then(response => {
                if (response.ok) {
                    // Redirect to the result page
                    window.location.href = response.url;
                } else {
                    throw new Error('Network response was not ok');
                }
            })
            .catch(error => {
                console.error('Error processing image:', error);
                alert('Có lỗi xảy ra khi xử lý ảnh');

                // Restore button state
                searchButton.innerHTML = originalText;
                searchButton.disabled = false;
            })
            .finally(() => {
                closeVisualSearchModal();
            });
    }

    // Handle file upload via drag and drop or click for visual search
    document.addEventListener('DOMContentLoaded', function() {
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('imageUpload');

        if (uploadArea && fileInput) {
            uploadArea.addEventListener('click', () => fileInput.click());
            
            uploadArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                uploadArea.classList.add('border-blue-400');
            });

            uploadArea.addEventListener('dragleave', () => {
                uploadArea.classList.remove('border-blue-400');
            });

            uploadArea.addEventListener('drop', (e) => {
                e.preventDefault();
                uploadArea.classList.remove('border-blue-400');
                const files = e.dataTransfer.files;
                if (files[0] && files[0].type.startsWith('image/')) {
                    handleImageUpload(files[0]);
                }
            });

            fileInput.addEventListener('change', (e) => {
                if (e.target.files[0]) {
                    handleImageUpload(e.target.files[0]);
                }
            });
        }
    });

    // Handle all add to cart forms globally
    document.addEventListener('submit', function(e) {
        const form = e.target;
        if (form.matches('form[action^="/add-to-cart/"]')) {
            e.preventDefault();
            
            fetch(form.action, {
                method: 'POST',
                body: new FormData(form),
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    showToast(data.message, 'success');
                    // Update cart count
                    const cartCount = document.querySelector('.cart-count');
                    if (cartCount) {
                        cartCount.textContent = data.cart_count;
                    }
                } else {
                    showToast(data.message, 'error');
                }
            })
            .catch(error => {
                showToast('Có lỗi xảy ra. Vui lòng thử lại.', 'error');
            });
        }
    });
    </script>
</body>
</html> 