# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
msgid ""
msgstr ""
"Project-Id-Version: \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-01-23 12:50-0600\n"
"PO-Revision-Date: 2023-11-12 20:04+0100\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: \n"
"Language: sk\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n == 1 ? 0 : n % 1 == 0 && n "
">= 2 && n <= 4 ? 1 : n % 1 != 0 ? 2: 3);\n"
"X-Translated-Using: django-rosetta 0.9.9\n"

#: account/adapter.py:50
msgid "Username can not be used. Please use other username."
msgstr "Užívateľské meno nemôže byť použité. Prosím, použite iné meno."

#: account/adapter.py:56
msgid "Too many failed login attempts. Try again later."
msgstr "Príliš veľa neúspešných pokusov o prihlásenie. Skúste neskôr."

#: account/adapter.py:58
msgid "A user is already registered with this email address."
msgstr "Používateľ s touto e-mailovou adresou už existuje."

#: account/adapter.py:59
msgid "Please type your current password."
msgstr "Prosím, napíšte svoje súčasné heslo."

#: account/adapter.py:60
msgid "Incorrect password."
msgstr "Nesprávne heslo"

#: account/adapter.py:61
#, python-brace-format
msgid "Password must be a minimum of {0} characters."
msgstr "Heslo musí mať aspoň {0} znakov."

#: account/adapter.py:62
msgid "The email address is not assigned to any user account"
msgstr ""
"Táto e-mailová adresa nie je pridelená k žiadnemu používateľskému kontu"

#: account/adapter.py:739
msgid "Use your password"
msgstr "Použite svoje heslo"

#: account/adapter.py:749
msgid "Use your authenticator app"
msgstr "použite autentifikačnú aplikáciu"

#: account/admin.py:23
#, fuzzy
#| msgid "Your primary email address must be verified."
msgid "Mark selected email addresses as verified"
msgstr "Primárna e-mailová adresa musí byť overená."

#: account/apps.py:11
msgid "Accounts"
msgstr "Účty"

#: account/forms.py:60 account/forms.py:449
msgid "You must type the same password each time."
msgstr "Heslá sa nezhodujú."

#: account/forms.py:92 account/forms.py:412 account/forms.py:554
#: account/forms.py:683
msgid "Password"
msgstr "Heslo"

#: account/forms.py:93
msgid "Remember Me"
msgstr "Zapamätať si ma"

#: account/forms.py:97
msgid "This account is currently inactive."
msgstr "Tento účet nie je momentálne aktívny."

#: account/forms.py:99
msgid "The email address and/or password you specified are not correct."
msgstr "Uvedený e-mail alebo heslo nie je správne."

#: account/forms.py:102
msgid "The username and/or password you specified are not correct."
msgstr "Uvedené užívateľské meno alebo heslo nie je správne."

#: account/forms.py:112 account/forms.py:287 account/forms.py:477
#: account/forms.py:574
msgid "Email address"
msgstr "E-mailová adresa"

#: account/forms.py:116 account/forms.py:325 account/forms.py:474
#: account/forms.py:569
msgid "Email"
msgstr "E-mail"

#: account/forms.py:119 account/forms.py:122 account/forms.py:277
#: account/forms.py:280
msgid "Username"
msgstr "Užívateľské meno"

#: account/forms.py:132
msgid "Username or email"
msgstr "Užívateľské meno alebo e-mail"

#: account/forms.py:135
msgctxt "field label"
msgid "Login"
msgstr "Login"

#: account/forms.py:146
msgid "Forgot your password?"
msgstr "Zabudnuté heslo?"

#: account/forms.py:316
msgid "Email (again)"
msgstr "E-mail (znova)"

#: account/forms.py:320
msgid "Email address confirmation"
msgstr "Potvrdenie e-mailu"

#: account/forms.py:328
msgid "Email (optional)"
msgstr "E-mail (nepovinné)"

#: account/forms.py:383
msgid "You must type the same email each time."
msgstr "E-maily sa nezhodujú."

#: account/forms.py:418 account/forms.py:557
msgid "Password (again)"
msgstr "Heslo (znovu)"

#: account/forms.py:489
msgid "This email address is already associated with this account."
msgstr "Táto e-mailová adresa je už spojená s týmto účtom."

#: account/forms.py:491
#, python-format
msgid "You cannot add more than %d email addresses."
msgstr "Nemôžte pridať viac než %d e-mailových adries."

#: account/forms.py:529
msgid "Current Password"
msgstr "Súčasné heslo"

#: account/forms.py:532 account/forms.py:632
msgid "New Password"
msgstr "Nové heslo"

#: account/forms.py:535 account/forms.py:633
msgid "New Password (again)"
msgstr "Nové heslo (znovu)"

#: account/forms.py:653
msgid "The password reset token was invalid."
msgstr "Token na obnovu hesla bol nesprávny."

#: account/models.py:21
msgid "user"
msgstr "používateľ"

#: account/models.py:26 account/models.py:34 account/models.py:138
msgid "email address"
msgstr "e-mailová adresa"

#: account/models.py:28
msgid "verified"
msgstr "overený"

#: account/models.py:29
msgid "primary"
msgstr "primárny"

#: account/models.py:35
msgid "email addresses"
msgstr "e-mailové adresy"

#: account/models.py:141
msgid "created"
msgstr "vytvorený"

#: account/models.py:142
msgid "sent"
msgstr "odoslané"

#: account/models.py:143 socialaccount/models.py:63
msgid "key"
msgstr "kľúč"

#: account/models.py:148
msgid "email confirmation"
msgstr "potvrdenie e-mailu"

#: account/models.py:149
msgid "email confirmations"
msgstr "potvrdenia e-mailu"

#: mfa/adapter.py:20
msgid ""
"You cannot activate two-factor authentication until you have verified your "
"email address."
msgstr ""
"Nemôžete aktivovať dvojfaktorovú autentifikáciu pokiaľ nemáte verifikovanú "
"svoju adresu."

#: mfa/adapter.py:23
msgid ""
"You cannot add an email address to an account protected by two-factor "
"authentication."
msgstr ""
"Nemôžete si pridať ďalšiu emailovú adresu do účtu s dvojfaktorovou "
"autentifikáciou."

#: mfa/adapter.py:25
msgid "Incorrect code."
msgstr "Nesprávny kód."

#: mfa/adapter.py:27
#, fuzzy
#| msgid ""
#| "You cannot add an email address to an account protected by two-factor "
#| "authentication."
msgid "You cannot deactivate two-factor authentication."
msgstr ""
"Nemôžete si pridať ďalšiu emailovú adresu do účtu s dvojfaktorovou "
"autentifikáciou."

#: mfa/apps.py:9
msgid "MFA"
msgstr "MFA"

#: mfa/forms.py:15 mfa/forms.py:17 mfa/forms.py:52
msgid "Code"
msgstr "Kód"

#: mfa/forms.py:50
msgid "Authenticator code"
msgstr "Kód z autentifikátora"

#: mfa/models.py:19
msgid "Recovery codes"
msgstr "Kódy obnovy"

#: mfa/models.py:20
msgid "TOTP Authenticator"
msgstr "TOTP autentifikátor"

#: socialaccount/adapter.py:31
#, python-format
msgid ""
"An account already exists with this email address. Please sign in to that "
"account first, then connect your %s account."
msgstr ""
"Účet s touto e-mailovou adresou už existuje. Prosím, prihláste sa najprv pod "
"daným účtom a potom pripojte svoj %s účet."

#: socialaccount/adapter.py:148
msgid "Your account has no password set up."
msgstr "Váš účet nemá nastavené heslo."

#: socialaccount/adapter.py:155
msgid "Your account has no verified email address."
msgstr "Váš účet nemá overenú e-mailovú adresu."

#: socialaccount/apps.py:9
msgid "Social Accounts"
msgstr "Účty na sociálnych sieťach"

#: socialaccount/models.py:37 socialaccount/models.py:91
msgid "provider"
msgstr "poskytovateľ"

#: socialaccount/models.py:46
msgid "provider ID"
msgstr "ID poskytovateľa"

#: socialaccount/models.py:50
msgid "name"
msgstr "meno"

#: socialaccount/models.py:52
msgid "client id"
msgstr "identifikátor klienta"

#: socialaccount/models.py:54
msgid "App ID, or consumer key"
msgstr "ID aplikácie alebo zákaznícky kľúč"

#: socialaccount/models.py:57
msgid "secret key"
msgstr "tajný kľúč"

#: socialaccount/models.py:60
msgid "API secret, client secret, or consumer secret"
msgstr "Kľúč API, klienta alebo zákazníka"

#: socialaccount/models.py:63
msgid "Key"
msgstr "Kľúč"

#: socialaccount/models.py:75
msgid "social application"
msgstr "sociálna aplikácia"

#: socialaccount/models.py:76
msgid "social applications"
msgstr "sociálne aplikácie"

#: socialaccount/models.py:111
msgid "uid"
msgstr "uid"

#: socialaccount/models.py:113
msgid "last login"
msgstr "posledné prihlásenie"

#: socialaccount/models.py:114
msgid "date joined"
msgstr "dáum pripojenia"

#: socialaccount/models.py:115
msgid "extra data"
msgstr "ďalšie údaje"

#: socialaccount/models.py:119
msgid "social account"
msgstr "sociálny účet"

#: socialaccount/models.py:120
msgid "social accounts"
msgstr "sociálne účty"

#: socialaccount/models.py:154
msgid "token"
msgstr "token"

#: socialaccount/models.py:155
msgid "\"oauth_token\" (OAuth1) or access token (OAuth2)"
msgstr ""
"\"Oauth_token\" (Podpora protokolu OAuth1) alebo prístup tokenu (OAuth2)"

#: socialaccount/models.py:159
msgid "token secret"
msgstr "heslo prístupového tokenu"

#: socialaccount/models.py:160
msgid "\"oauth_token_secret\" (OAuth1) or refresh token (OAuth2)"
msgstr ""
"\"Oauth_token_secret\" (Podpora protokolu OAuth1) alebo token obnovenie "
"(OAuth2)"

#: socialaccount/models.py:163
msgid "expires at"
msgstr "vyexpiruje"

#: socialaccount/models.py:168
msgid "social application token"
msgstr "token sociálnej aplikácie"

#: socialaccount/models.py:169
msgid "social application tokens"
msgstr "tokeny sociálnej aplikácie"

#: socialaccount/providers/douban/views.py:37
msgid "Invalid profile data"
msgstr "Nesprávne profilové údaje"

#: socialaccount/providers/oauth/client.py:85
#, python-format
msgid ""
"Invalid response while obtaining request token from \"%s\". Response was: %s."
msgstr ""
"Neplatná odpoveď pri získavaní požiadavky tokenu z  \"%s\". Odpoveď: %s."

#: socialaccount/providers/oauth/client.py:119
#: socialaccount/providers/pocket/client.py:86
#, python-format
msgid "Invalid response while obtaining access token from \"%s\"."
msgstr "Neplatná odozva pri získavaní prístupu tokenu z \"%s\"."

#: socialaccount/providers/oauth/client.py:140
#, python-format
msgid "No request token saved for \"%s\"."
msgstr "Žiadna uložená požiadavka tokenu pre \"%s\"."

#: socialaccount/providers/oauth/client.py:191
#, python-format
msgid "No access token saved for \"%s\"."
msgstr "Žiadny uložený prístupový token pre \"%s\"."

#: socialaccount/providers/oauth/client.py:212
#, python-format
msgid "No access to private resources at \"%s\"."
msgstr "Žiadny prístup do privátneho úložiska na \"%s\"."

#: socialaccount/providers/pocket/client.py:41
#, python-format
msgid "Invalid response while obtaining request token from \"%s\"."
msgstr "Neplatná odozva pri získavaní požiadavky tokenu z  \"%s\"."

#: templates/account/account_inactive.html:5
#: templates/account/account_inactive.html:9
msgid "Account Inactive"
msgstr "Účet neaktívny"

#: templates/account/account_inactive.html:11
msgid "This account is inactive."
msgstr "Tento účet je neaktívny."

#: templates/account/base_reauthenticate.html:5
#: templates/account/base_reauthenticate.html:9
msgid "Confirm Access"
msgstr "Potvrdiť prístup"

#: templates/account/base_reauthenticate.html:11
msgid "Please reauthenticate to safeguard your account."
msgstr "Znova sa overte, aby ste ochránili svoj účet."

#: templates/account/base_reauthenticate.html:17
msgid "Alternative options"
msgstr "Alternatívne možnosti"

#: templates/account/email.html:4 templates/account/email.html:8
msgid "Email Addresses"
msgstr "E-mailová adresa"

#: templates/account/email.html:11
msgid "The following email addresses are associated with your account:"
msgstr "Nasledujúce e-mailové adresy sú prepojené s vašim účtom:"

#: templates/account/email.html:23
msgid "Verified"
msgstr "Overený"

#: templates/account/email.html:27
msgid "Unverified"
msgstr "Neoverený"

#: templates/account/email.html:32
msgid "Primary"
msgstr "Primárny"

#: templates/account/email.html:42
msgid "Make Primary"
msgstr "Vytvoriť primárny"

#: templates/account/email.html:45 templates/account/email_change.html:37
msgid "Re-send Verification"
msgstr "Preposlať overenie"

#: templates/account/email.html:48 templates/socialaccount/connections.html:40
msgid "Remove"
msgstr "Odstrániť"

#: templates/account/email.html:57
msgid "Add Email Address"
msgstr "Pridať e-mailovú adresu"

#: templates/account/email.html:70
msgid "Add Email"
msgstr "Pridať e-mail"

#: templates/account/email.html:79
msgid "Do you really want to remove the selected email address?"
msgstr "Naozaj chcete odstrániť vybranú e-mailovú adresu?"

#: templates/account/email/account_already_exists_message.txt:4
#, python-format
msgid ""
"You are receiving this email because you or someone else tried to signup for "
"an\n"
"account using email address:\n"
"\n"
"%(email)s\n"
"\n"
"However, an account using that email address already exists.  In case you "
"have\n"
"forgotten about this, please use the password forgotten procedure to "
"recover\n"
"your account:\n"
"\n"
"%(password_reset_url)s"
msgstr ""
"Tento e-mail ste dostali, pretože ste sa Vy, alebo niekto iný\n"
"pokúšali vytvoriť účet pre e-mailovu adresu:\n"
"\n"
"%(email)s\n"
"\n"
"Účet s touto e-mailovou adresou už však existuje.  V prípade,\n"
"že ste na svoju registráciu zabudli, použite prosím funkciu obnovy hesla k "
"Vášmu účtu:\n"
"\n"
"%(password_reset_url)s"

#: templates/account/email/account_already_exists_subject.txt:3
msgid "Account Already Exists"
msgstr "Účet už existuje"

#: templates/account/email/base_message.txt:1
#, python-format
msgid "Hello from %(site_name)s!"
msgstr "Dobrý deň z %(site_name)s!"

#: templates/account/email/base_message.txt:5
#, python-format
msgid ""
"Thank you for using %(site_name)s!\n"
"%(site_domain)s"
msgstr ""
"Ďakujeme za využitie %(site_name)s!\n"
"%(site_domain)s"

#: templates/account/email/base_notification.txt:5
msgid ""
"You are receiving this mail because the following change was made to your "
"account:"
msgstr ""

#: templates/account/email/base_notification.txt:10
#, python-format
msgid ""
"If you do not recognize this change then please take proper security "
"precautions immediately. The change to your account originates from:\n"
"\n"
"- IP address: %(ip)s\n"
"- Browser: %(user_agent)s\n"
"- Date: %(timestamp)s"
msgstr ""

#: templates/account/email/email_changed_message.txt:4
#, python-format
msgid "Your email has been changed from %(from_email)s to %(to_email)s."
msgstr ""

#: templates/account/email/email_changed_subject.txt:3
#, fuzzy
#| msgid "Cancel"
msgid "Email Changed"
msgstr "Zrušiť"

#: templates/account/email/email_confirm_message.txt:4
#, fuzzy
#| msgid "You have confirmed %(email)s."
msgid "Your email has been confirmed."
msgstr "%(email)s potvrdené."

#: templates/account/email/email_confirm_subject.txt:3
#, fuzzy
#| msgid "email confirmation"
msgid "Email Confirmation"
msgstr "potvrdenie e-mailu"

#: templates/account/email/email_confirmation_message.txt:5
#, python-format
msgid ""
"You're receiving this email because user %(user_display)s has given your "
"email address to register an account on %(site_domain)s.\n"
"\n"
"To confirm this is correct, go to %(activate_url)s"
msgstr ""
"Tento e-mail ste dostali, pretože používateľ %(user_display)s na "
"%(site_domain)s zadal túto e-mailovú adresu na prepojenie s jeho účtom.\n"
"\n"
"Pre potvrdenie nasledujte %(activate_url)s"

#: templates/account/email/email_confirmation_subject.txt:3
msgid "Please Confirm Your Email Address"
msgstr "Potvrďte prosím svoju e-mailovú adresu"

#: templates/account/email/email_deleted_message.txt:4
#, python-format
msgid "Email address %(deleted_email)s has been removed from your account."
msgstr ""

#: templates/account/email/email_deleted_subject.txt:3
#, fuzzy
#| msgid "Remove"
msgid "Email Removed"
msgstr "Odstrániť"

#: templates/account/email/password_changed_message.txt:4
#, fuzzy
#| msgid "Your password is now changed."
msgid "Your password has been changed."
msgstr "Tvoje heslo bolo zmenené."

#: templates/account/email/password_changed_subject.txt:3
#, fuzzy
#| msgid "Password (again)"
msgid "Password Changed"
msgstr "Heslo (znovu)"

#: templates/account/email/password_reset_key_message.txt:4
msgid ""
"You're receiving this email because you or someone else has requested a "
"password reset for your user account.\n"
"It can be safely ignored if you did not request a password reset. Click the "
"link below to reset your password."
msgstr ""
"Tento e-mail ste dostali, pretože niekto požiadal o heslo k Vášmu "
"používateľskému účtu. Ak ste to neboli Vy, správu môžete pokojne ignorovať. "
"Odkaz nižšie obnoví heslo."

#: templates/account/email/password_reset_key_message.txt:9
#, python-format
msgid "In case you forgot, your username is %(username)s."
msgstr "Ak ste náhodou zabudli, vaše používateľské meno je %(username)s."

#: templates/account/email/password_reset_key_subject.txt:3
#: templates/account/email/unknown_account_subject.txt:3
msgid "Password Reset Email"
msgstr "E-mail pre obnovu hesla"

#: templates/account/email/password_reset_message.txt:4
#, fuzzy
#| msgid "Your password is now changed."
msgid "Your password has been reset."
msgstr "Tvoje heslo bolo zmenené."

#: templates/account/email/password_reset_subject.txt:3
#: templates/account/password_reset.html:4
#: templates/account/password_reset.html:8
#: templates/account/password_reset_done.html:6
#: templates/account/password_reset_done.html:10
msgid "Password Reset"
msgstr "Obnoviť heslo"

#: templates/account/email/password_set_message.txt:4
#, fuzzy
#| msgid "Your password is now changed."
msgid "Your password has been set."
msgstr "Tvoje heslo bolo zmenené."

#: templates/account/email/password_set_subject.txt:3
#, fuzzy
#| msgid "Password Reset"
msgid "Password Set"
msgstr "Obnoviť heslo"

#: templates/account/email/unknown_account_message.txt:4
#, python-format
msgid ""
"You are receiving this email because you or someone else has requested a\n"
"password for your user account. However, we do not have any record of a "
"user\n"
"with email %(email)s in our database.\n"
"\n"
"This mail can be safely ignored if you did not request a password reset.\n"
"\n"
"If it was you, you can sign up for an account using the link below."
msgstr ""
"Tento e-mail ste dostali, pretože niekto požiadal o heslo k Vášmu\n"
"používateľskému účtu. V našej databáze však nemáme žiadny záznam o "
"používateľovi\n"
"s e-mailom %(email)s.\n"
"\n"
"Ak ste nežidali o obnovenie hesla, môžete tento e-mail pokojne ignorovať.\n"
"\n"
"Ak ste to boli Vy, môžete si zaregistrovať účet pomocou odkazu nižšie."

#: templates/account/email_change.html:5 templates/account/email_change.html:9
msgid "Email Address"
msgstr "E-mailová adresa"

#: templates/account/email_change.html:21
#: templates/account/email_change.html:29
#, fuzzy
#| msgid "Current Password"
msgid "Current email"
msgstr "Súčasné heslo"

#: templates/account/email_change.html:31
msgid "Changing to"
msgstr ""

#: templates/account/email_change.html:35
#, fuzzy
#| msgid "Your email address is still pending verification:"
msgid "Your email address is still pending verification."
msgstr "Vaša e-mailová adresa stále čaká na overenie:"

#: templates/account/email_change.html:41
#, fuzzy
#| msgid "Cancel"
msgid "Cancel Change"
msgstr "Zrušiť"

#: templates/account/email_change.html:49
#, fuzzy
#| msgid "Change Email"
msgid "Change to"
msgstr "Zmeniť e-mail"

#: templates/account/email_change.html:55
#: templates/allauth/layouts/base.html:29
msgid "Change Email"
msgstr "Zmeniť e-mail"

#: templates/account/email_confirm.html:6
#: templates/account/email_confirm.html:10
msgid "Confirm Email Address"
msgstr "Potvrdiť e-mailovú adresu"

#: templates/account/email_confirm.html:16
#, python-format
msgid ""
"Please confirm that <a href=\"mailto:%(email)s\">%(email)s</a> is an email "
"address for user %(user_display)s."
msgstr ""
"Prosím potvrďte, že <a href=\"mailto:%(email)s\">%(email)s</a> je e-mailová "
"adresa pre používateľa %(user_display)s."

#: templates/account/email_confirm.html:23
#: templates/account/reauthenticate.html:20
#: templates/mfa/reauthenticate.html:20
msgid "Confirm"
msgstr "Potvrdiť"

#: templates/account/email_confirm.html:29
#: templates/account/messages/email_confirmation_failed.txt:2
#, python-format
msgid ""
"Unable to confirm %(email)s because it is already confirmed by a different "
"account."
msgstr "Nemožno potvrdiť %(email)s pretože už je potvrdený iným používateľom."

#: templates/account/email_confirm.html:35
#, python-format
msgid ""
"This email confirmation link expired or is invalid. Please <a "
"href=\"%(email_url)s\">issue a new email confirmation request</a>."
msgstr ""
"Odkaz na potvrdenie e-mailu je neplatný alebo vypršal. <a "
"href=\"%(email_url)s\">Zaslať novú žiadosť o overovací e-mail</a>."

#: templates/account/login.html:5 templates/account/login.html:9
#: templates/account/login.html:29 templates/allauth/layouts/base.html:36
#: templates/mfa/authenticate.html:5 templates/mfa/authenticate.html:23
#: templates/openid/login.html:5 templates/openid/login.html:9
#: templates/openid/login.html:20 templates/socialaccount/login.html:5
msgid "Sign In"
msgstr "Prihlásiť sa"

#: templates/account/login.html:12
#, python-format
msgid ""
"If you have not created an account yet, then please\n"
"    <a href=\"%(signup_url)s\">sign up</a> first."
msgstr ""
"Ak ešte nemáte vytvorený účet, tak potom sa prosím\n"
"najprv <a href=\"%(signup_url)s\">zaregistrujte</a>."

#: templates/account/logout.html:4 templates/account/logout.html:8
#: templates/account/logout.html:23 templates/allauth/layouts/base.html:32
#: templates/usersessions/usersession_list.html:64
msgid "Sign Out"
msgstr "Odhlásiť"

#: templates/account/logout.html:10
msgid "Are you sure you want to sign out?"
msgstr "Ste si istý, že sa chcete odhlásiť?"

#: templates/account/messages/cannot_delete_primary_email.txt:2
#, python-format
msgid "You cannot remove your primary email address (%(email)s)."
msgstr "Primárna e-mailová adresa sa nedá odstrániť (%(email)s)."

#: templates/account/messages/email_confirmation_sent.txt:2
#, python-format
msgid "Confirmation email sent to %(email)s."
msgstr "Overovací e-mail poslaný na %(email)s."

#: templates/account/messages/email_confirmed.txt:2
#, python-format
msgid "You have confirmed %(email)s."
msgstr "%(email)s potvrdené."

#: templates/account/messages/email_deleted.txt:2
#, python-format
msgid "Removed email address %(email)s."
msgstr "E-mailová adresa %(email)s úpešne odstránená."

#: templates/account/messages/logged_in.txt:4
#, python-format
msgid "Successfully signed in as %(name)s."
msgstr "Úspešne ste sa prihlásili ako %(name)s."

#: templates/account/messages/logged_out.txt:2
msgid "You have signed out."
msgstr "Odhlásili ste sa."

#: templates/account/messages/password_changed.txt:2
msgid "Password successfully changed."
msgstr "Zmena hesla prebehla úspešne."

#: templates/account/messages/password_set.txt:2
msgid "Password successfully set."
msgstr ")Nastavenie hesla bolo úspešné."

#: templates/account/messages/primary_email_set.txt:2
msgid "Primary email address set."
msgstr "Primárna e-mailová adresa bola úspešne zadaná."

#: templates/account/messages/unverified_primary_email.txt:2
msgid "Your primary email address must be verified."
msgstr "Primárna e-mailová adresa musí byť overená."

#: templates/account/password_change.html:4
#: templates/account/password_change.html:8
#: templates/account/password_change.html:19
#: templates/account/password_reset_from_key.html:5
#: templates/account/password_reset_from_key.html:12
#: templates/account/password_reset_from_key.html:29
#: templates/account/password_reset_from_key_done.html:5
#: templates/account/password_reset_from_key_done.html:9
msgid "Change Password"
msgstr "Zmeniť heslo"

#: templates/account/password_change.html:21
msgid "Forgot Password?"
msgstr "Zabudnuté heslo?"

#: templates/account/password_reset.html:14
msgid ""
"Forgotten your password? Enter your email address below, and we'll send you "
"an email allowing you to reset it."
msgstr ""
"Zabudli ste heslo? Vložte nižšie svoju e-mailovú adresu a čoskoro vám "
"pošleme e-mail na obnovenie hesla."

#: templates/account/password_reset.html:25
msgid "Reset My Password"
msgstr "Obnov moje heslo"

#: templates/account/password_reset.html:29
msgid "Please contact us if you have any trouble resetting your password."
msgstr ""
"Prosím, kontaktujte nás, ak máte nejaký problém s obnovením svojho hesla."

#: templates/account/password_reset_done.html:16
msgid ""
"We have sent you an email. If you have not received it please check your "
"spam folder. Otherwise contact us if you do not receive it in a few minutes."
msgstr ""
"Poslali sme vám e-mail.  Ak ste ho nedostali, skontrolujte si priečinok so "
"spamom.  Ak ste v priebehu pár minút žiaden neobdržali, kontaktujte nás."

#: templates/account/password_reset_from_key.html:10
msgid "Bad Token"
msgstr "Zlý token"

#: templates/account/password_reset_from_key.html:18
#, python-format
msgid ""
"The password reset link was invalid, possibly because it has already been "
"used.  Please request a <a href=\"%(passwd_reset_url)s\">new password reset</"
"a>."
msgstr ""
"Odkaz na obnovu heslo je neplatný, pravdepodobne už bol použitý. <a "
"href=\"%(passwd_reset_url)s\">Nové obnovenie hesla</a>."

#: templates/account/password_reset_from_key_done.html:11
msgid "Your password is now changed."
msgstr "Tvoje heslo bolo zmenené."

#: templates/account/password_set.html:5 templates/account/password_set.html:9
#: templates/account/password_set.html:20
msgid "Set Password"
msgstr "Nastaviť heslo"

#: templates/account/reauthenticate.html:5
msgid "Enter your password:"
msgstr "Zadajte svoje heslo:"

#: templates/account/signup.html:4 templates/socialaccount/signup.html:5
msgid "Signup"
msgstr "Registrácia"

#: templates/account/signup.html:8 templates/account/signup.html:27
#: templates/allauth/layouts/base.html:39 templates/socialaccount/signup.html:9
#: templates/socialaccount/signup.html:29
msgid "Sign Up"
msgstr "Zaregistrovať"

#: templates/account/signup.html:11
#, python-format
msgid ""
"Already have an account? Then please <a href=\"%(login_url)s\">sign in</a>."
msgstr ""
"Už ste sa zaregistrovali? Tak sa <a href=\"%(login_url)s\">prihláste</a>."

#: templates/account/signup_closed.html:5
#: templates/account/signup_closed.html:9
msgid "Sign Up Closed"
msgstr "Registrácia uzavretá"

#: templates/account/signup_closed.html:11
msgid "We are sorry, but the sign up is currently closed."
msgstr "Ospravedlňujeme sa, ale registrácia je momentálne uzavretá."

#: templates/account/snippets/already_logged_in.html:7
msgid "Note"
msgstr "Poznámka"

#: templates/account/snippets/already_logged_in.html:7
#, python-format
msgid "You are already logged in as %(user_display)s."
msgstr "Už ste prihlásený ako %(user_display)s."

#: templates/account/snippets/warn_no_email.html:3
msgid "Warning:"
msgstr "Varovanie:"

#: templates/account/snippets/warn_no_email.html:3
msgid ""
"You currently do not have any email address set up. You should really add an "
"email address so you can receive notifications, reset your password, etc."
msgstr ""
"Momentálne nemáte nastavený žiaden e-mail, kvôli čomu nemôžete dostávať "
"upozornenia, obnovovať heslo, atď."

#: templates/account/verification_sent.html:5
#: templates/account/verification_sent.html:9
#: templates/account/verified_email_required.html:5
#: templates/account/verified_email_required.html:9
msgid "Verify Your Email Address"
msgstr "Potvrďte e-mailovú adresu"

#: templates/account/verification_sent.html:12
msgid ""
"We have sent an email to you for verification. Follow the link provided to "
"finalize the signup process. If you do not see the verification email in "
"your main inbox, check your spam folder. Please contact us if you do not "
"receive the verification email within a few minutes."
msgstr ""
"Poslali sme Vám overovací e-mail. Kliknutím na uvedený odkaz dokončite "
"proces registrácie. Ak ste ho nedostali, skontrolujte si priečinok so "
"spamom. V prípade, že do niekoľkých minút nedostanete overovací e-mail, "
"kontaktujte nás."

#: templates/account/verified_email_required.html:13
msgid ""
"This part of the site requires us to verify that\n"
"you are who you claim to be. For this purpose, we require that you\n"
"verify ownership of your email address. "
msgstr ""
"Na prezeranie nasledujúceho obsahu je potrebné overenie vašej e-mailovej "
"adresy. "

#: templates/account/verified_email_required.html:18
msgid ""
"We have sent an email to you for\n"
"verification. Please click on the link inside that email. If you do not see "
"the verification email in your main inbox, check your spam folder. "
"Otherwise\n"
"contact us if you do not receive it within a few minutes."
msgstr ""
"Poslali sme Vám overovací e-mail. Kliknite prosím na odkaz v e-maili. Ak ste "
"e-mail neobdržali, skontrolujte váš spam priečinok. V prípade, že ho do "
"niekoľkých minút nedostanete, kontaktujte nás."

#: templates/account/verified_email_required.html:23
#, python-format
msgid ""
"<strong>Note:</strong> you can still <a href=\"%(email_url)s\">change your "
"email address</a>."
msgstr ""
"<strong>Poznámka:</strong> stále môžete <a href=\"%(email_url)s\">zmeniť "
"svoju e-mailovú adresu</a>."

#: templates/allauth/layouts/base.html:18
msgid "Messages:"
msgstr "Správy:"

#: templates/allauth/layouts/base.html:25
msgid "Menu:"
msgstr "Menu:"

#: templates/mfa/authenticate.html:9 templates/mfa/index.html:5
#: templates/mfa/index.html:9
msgid "Two-Factor Authentication"
msgstr "Dvojfaktorová autentifikácia"

#: templates/mfa/authenticate.html:12
msgid ""
"Your account is protected by two-factor authentication. Please enter an "
"authenticator code:"
msgstr ""
"Váš účet je chránený dvojfaktorovu autentifikáciou. Prosím zadajte kód z "
"autentifikátora:"

#: templates/mfa/authenticate.html:27
msgid "Cancel"
msgstr "Zrušiť"

#: templates/mfa/email/recovery_codes_generated_message.txt:4
#, fuzzy
#| msgid "A new set of recovery codes has been generated."
msgid ""
"A new set of Two-Factor Authentication recovery codes has been generated."
msgstr "Boli vygenerované nové kódy obnovy."

#: templates/mfa/email/recovery_codes_generated_subject.txt:3
#, fuzzy
#| msgid "Recovery Codes"
msgid "New Recovery Codes Generated"
msgstr "Kódy obnovy"

#: templates/mfa/email/totp_activated_message.txt:4
#: templates/mfa/messages/totp_activated.txt:2
msgid "Authenticator app activated."
msgstr "Autentifikačná aplikácia aktivovaná."

#: templates/mfa/email/totp_activated_subject.txt:3
#, fuzzy
#| msgid "Authenticator app activated."
msgid "Authenticator App Activated"
msgstr "Autentifikačná aplikácia aktivovaná."

#: templates/mfa/email/totp_deactivated_message.txt:4
#: templates/mfa/messages/totp_deactivated.txt:2
msgid "Authenticator app deactivated."
msgstr "Autentifikačná aplikácia deaktivovaná."

#: templates/mfa/email/totp_deactivated_subject.txt:3
#, fuzzy
#| msgid "Authenticator app deactivated."
msgid "Authenticator App Deactivated"
msgstr "Autentifikačná aplikácia deaktivovaná."

#: templates/mfa/index.html:14 templates/mfa/totp/base.html:4
msgid "Authenticator App"
msgstr "Autentifikačná aplikácia"

#: templates/mfa/index.html:18
msgid "Authentication using an authenticator app is active."
msgstr "Autentifikácia pomocou aplikácie je aktívna."

#: templates/mfa/index.html:20
msgid "An authenticator app is not active."
msgstr "Autentifikačná aplikácia je neaktívna."

#: templates/mfa/index.html:28 templates/mfa/totp/deactivate_form.html:24
msgid "Deactivate"
msgstr "Deaktivovať"

#: templates/mfa/index.html:32 templates/mfa/totp/activate_form.html:32
msgid "Activate"
msgstr "Aktivovať"

#: templates/mfa/index.html:42 templates/mfa/recovery_codes/base.html:4
#: templates/mfa/recovery_codes/generate.html:6
#: templates/mfa/recovery_codes/index.html:6
msgid "Recovery Codes"
msgstr "Kódy obnovy"

#: templates/mfa/index.html:47 templates/mfa/recovery_codes/index.html:9
#, python-format
msgid ""
"There is %(unused_count)s out of %(total_count)s recovery codes available."
msgid_plural ""
"There are %(unused_count)s out of %(total_count)s recovery codes available."
msgstr[0] ""
"Je k dispozícii %(unused_count)s kódov z celkového počtu %(total_count)s "
"kódov obnovy."
msgstr[1] ""
"Je k dispozícii %(unused_count)s kód z celkového počtu %(total_count)s kódov "
"obnovy."
msgstr[2] ""
"Je k dispozícii %(unused_count)s kódov z celkového počtu %(total_count)s "
"kódov obnovy."
msgstr[3] ""
"Je k dispozícii %(unused_count)s kódu z celkového počtu %(total_count)s "
"kódov obnovy."

#: templates/mfa/index.html:50
msgid "No recovery codes set up."
msgstr "Nie sú nastavené žiadne kódy obnovy."

#: templates/mfa/index.html:59
msgid "View"
msgstr "Zobraziť"

#: templates/mfa/index.html:65
msgid "Download"
msgstr "Stiahnuť"

#: templates/mfa/index.html:73 templates/mfa/recovery_codes/generate.html:29
msgid "Generate"
msgstr "Generovať"

#: templates/mfa/messages/recovery_codes_generated.txt:2
msgid "A new set of recovery codes has been generated."
msgstr "Boli vygenerované nové kódy obnovy."

#: templates/mfa/reauthenticate.html:5
msgid "Enter an authenticator code:"
msgstr "Zadajte kód z autentifikátora:"

#: templates/mfa/recovery_codes/generate.html:9
msgid "You are about to generate a new set of recovery codes for your account."
msgstr "Chystáte sa vygenerovať nové kódy obnovy pre váš účet."

#: templates/mfa/recovery_codes/generate.html:11
msgid "This action will invalidate your existing codes."
msgstr "Táto akcia zruší platnosť vašich existujúcich kódov."

#: templates/mfa/recovery_codes/generate.html:13
msgid "Are you sure?"
msgstr "Ste si istý?"

#: templates/mfa/recovery_codes/index.html:13
msgid "Unused codes"
msgstr "Nepoužité kódy"

#: templates/mfa/recovery_codes/index.html:25
msgid "Download codes"
msgstr "Stiahnuť kódy"

#: templates/mfa/recovery_codes/index.html:30
msgid "Generate new codes"
msgstr "Generovať nové kódy"

#: templates/mfa/totp/activate_form.html:4
#: templates/mfa/totp/activate_form.html:8
msgid "Activate Authenticator App"
msgstr "Aktivovať autentifikačnú aplikáciu"

#: templates/mfa/totp/activate_form.html:11
msgid ""
"To protect your account with two-factor authentication, scan the QR code "
"below with your authenticator app. Then, input the verification code "
"generated by the app below."
msgstr ""
"Na ochranu vášho účtu dvojfaktorovou autentifikáciou naskenujte QR kód "
"nižšie vašou autentifikačnou aplikáciou a následne zadajte nižšie "
"vygenerovaný overovací kód."

#: templates/mfa/totp/activate_form.html:21
msgid "Authenticator secret"
msgstr "Heslo prístupového tokenu"

#: templates/mfa/totp/activate_form.html:24
msgid ""
"You can store this secret and use it to reinstall your authenticator app at "
"a later time."
msgstr ""
"Môžete si uložiť toto heslo a kedykoľvek ho použiť na preinštalovanie vašej "
"autentifikačnej aplikaćie."

#: templates/mfa/totp/deactivate_form.html:5
#: templates/mfa/totp/deactivate_form.html:9
msgid "Deactivate Authenticator App"
msgstr "Deaktivovať autentifikačnú aplikáciu"

#: templates/mfa/totp/deactivate_form.html:12
msgid ""
"You are about to deactivate authenticator app based authentication. Are you "
"sure?"
msgstr "Chystáte sa deaktivovať vašu autentifikačnú aplikáciu. Ste si istý?"

#: templates/socialaccount/authentication_error.html:5
#: templates/socialaccount/authentication_error.html:9
#, fuzzy
#| msgid "Social Network Login Failure"
msgid "Third-Party Login Failure"
msgstr "Prihlasovanie pomocou sociálnej siete neúspešné"

#: templates/socialaccount/authentication_error.html:11
#, fuzzy
#| msgid ""
#| "An error occurred while attempting to login via your social network "
#| "account."
msgid ""
"An error occurred while attempting to login via your third-party account."
msgstr "Pri prihlasovaní pomocou sociálnej siete sa vyskytla chyba."

#: templates/socialaccount/connections.html:5
#: templates/socialaccount/connections.html:9
msgid "Account Connections"
msgstr "Pripojenia účtu"

#: templates/socialaccount/connections.html:13
msgid ""
"You can sign in to your account using any of the following third-party "
"accounts:"
msgstr "Môžete sa prihlásiť pomocou niektorého z nasledujúcich účtov:"

#: templates/socialaccount/connections.html:45
#, fuzzy
#| msgid ""
#| "You currently have no social network accounts connected to this account."
msgid "You currently have no third-party accounts connected to this account."
msgstr "Momentálne nemáte pripojený žiaden sociálny účet."

#: templates/socialaccount/connections.html:48
msgid "Add a Third-Party Account"
msgstr "Pridať účet tretej strany"

#: templates/socialaccount/email/account_connected_message.txt:4
#, python-format
msgid ""
"A third-party account from %(provider)s has been connected to your account."
msgstr ""

#: templates/socialaccount/email/account_connected_subject.txt:3
#, fuzzy
#| msgid "Add a Third-Party Account"
msgid "Third-Party Account Connected"
msgstr "Pridať účet tretej strany"

#: templates/socialaccount/email/account_disconnected_message.txt:4
#, python-format
msgid ""
"A third-party account from %(provider)s has been disconnected from your "
"account."
msgstr ""

#: templates/socialaccount/email/account_disconnected_subject.txt:3
#, fuzzy
#| msgid "Add a Third-Party Account"
msgid "Third-Party Account Disconnected"
msgstr "Pridať účet tretej strany"

#: templates/socialaccount/login.html:10
#, python-format
msgid "Connect %(provider)s"
msgstr "Prepojiť s %(provider)s"

#: templates/socialaccount/login.html:13
#, python-format
msgid "You are about to connect a new third-party account from %(provider)s."
msgstr "Chystáte sa prepojiť nový účet tretej strany %(provider)s."

#: templates/socialaccount/login.html:17
#, python-format
msgid "Sign In Via %(provider)s"
msgstr "Prihlásiť sa cez %(provider)s."

#: templates/socialaccount/login.html:20
#, python-format
msgid "You are about to sign in using a third-party account from %(provider)s."
msgstr "Chystáte sa prihlásiť cez účet tretej strany %(provider)s."

#: templates/socialaccount/login.html:27
msgid "Continue"
msgstr "Pokračovať"

#: templates/socialaccount/login_cancelled.html:5
#: templates/socialaccount/login_cancelled.html:9
msgid "Login Cancelled"
msgstr "Prihlásenie zrušené"

#: templates/socialaccount/login_cancelled.html:13
#, python-format
msgid ""
"You decided to cancel logging in to our site using one of your existing "
"accounts. If this was a mistake, please proceed to <a "
"href=\"%(login_url)s\">sign in</a>."
msgstr ""
"Rozhodli ste sa zrušiť prihlasovanie sa na našu stránku použitím jedného z "
"vašich existujúcich účtov. Ak se chceli vykonať inú operáciu, pokračujte na "
"<a href=\"%(login_url)s\">prihlásiť sa</a>."

#: templates/socialaccount/messages/account_connected.txt:2
#, fuzzy
#| msgid "The social account has been connected."
msgid "The third-party account has been connected."
msgstr "Sociálny účet bol pripojený."

#: templates/socialaccount/messages/account_connected_other.txt:2
#, fuzzy
#| msgid "The social account is already connected to a different account."
msgid "The third-party account is already connected to a different account."
msgstr "Tento sociálny účet už bol pripojený k inému používateľovi."

#: templates/socialaccount/messages/account_disconnected.txt:2
#, fuzzy
#| msgid "The social account has been disconnected."
msgid "The third-party account has been disconnected."
msgstr "Sociálny účet bol odpojený."

#: templates/socialaccount/signup.html:12
#, python-format
msgid ""
"You are about to use your %(provider_name)s account to login to\n"
"%(site_name)s. As a final step, please complete the following form:"
msgstr ""
"Chystáte sa použiť váš %(provider_name)s účet na prihlásenie do "
"%(site_name)s. Ako posledný krok vyplňte nasledujúci formulár:"

#: templates/socialaccount/snippets/login.html:9
msgid "Or use a third-party"
msgstr "Alebo použiť tretiu stranu"

#: templates/usersessions/messages/sessions_logged_out.txt:2
msgid "Signed out of all other sessions."
msgstr ""

#: templates/usersessions/usersession_list.html:6
#: templates/usersessions/usersession_list.html:10
msgid "Sessions"
msgstr ""

#: templates/usersessions/usersession_list.html:23
msgid "Started At"
msgstr ""

#: templates/usersessions/usersession_list.html:24
#, fuzzy
#| msgid "Email Address"
msgid "IP Address"
msgstr "E-mailová adresa"

#: templates/usersessions/usersession_list.html:25
msgid "Browser"
msgstr ""

#: templates/usersessions/usersession_list.html:27
msgid "Last seen at"
msgstr ""

#: templates/usersessions/usersession_list.html:47
#, fuzzy
#| msgid "Current Password"
msgid "Current"
msgstr "Súčasné heslo"

#: templates/usersessions/usersession_list.html:60
msgid "Sign Out Other Sessions"
msgstr ""

#: usersessions/apps.py:9
msgid "User Sessions"
msgstr ""

#: usersessions/models.py:48
msgid "session key"
msgstr ""

#~ msgid "The following email address is associated with your account:"
#~ msgstr "Nasledujúce e-mailové adresy sú prepojené s vašim účtom:"

#~ msgid "Change Email Address"
#~ msgstr "Potvrdiť e-mailovú adresu"

#~ msgid ""
#~ "To safeguard the security of your account, please enter your password:"
#~ msgstr "Kvôli bezpečnosti vášho účtu, zadajte prosím vaše heslo:"

#, python-format
#~ msgid ""
#~ "Please sign in with one\n"
#~ "of your existing third party accounts. Or, <a "
#~ "href=\"%(signup_url)s\">sign up</a>\n"
#~ "for a %(site_name)s account and sign in below:"
#~ msgstr ""
#~ "Prosím prihláste sa s jedným\n"
#~ "z vašich existujúcich účtov iných služieb. Alebo <a "
#~ "href=\"%(signup_url)s\">sa zaregistrujte</a>\n"
#~ "na %(site_name)s a prihláste sa nižšie:"

#~ msgid "or"
#~ msgstr "alebo"

#~ msgid "change password"
#~ msgstr "zmeniť heslo"

#~ msgid "OpenID Sign In"
#~ msgstr "Prihlásiť pomocou OpenID"

#~ msgid "This email address is already associated with another account."
#~ msgstr "Táto e-mailová adresa je už spojená s iným účtom."

#~ msgid ""
#~ "We have sent you an e-mail. Please contact us if you do not receive it "
#~ "within a few minutes."
#~ msgstr ""
#~ "Odoslali sme vám e-mail. Prosím kontaktujte nás ak ste ho nedostali do "
#~ "pár minút."
