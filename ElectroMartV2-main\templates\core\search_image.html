{% extends 'base.html' %}
{% load custom_filters %}

{% block title %}<PERSON><PERSON><PERSON> phẩm{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Breadcrumb -->
    <nav class="flex mb-8" aria-label="Breadcrumb">
        <ol class="inline-flex items-center space-x-1 md:space-x-3">
            <li class="inline-flex items-center">
                <a href="{% url 'home' %}" class="text-gray-700 hover:text-blue-600">
                    <svg class="w-5 h-5 mr-2.5" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"/>
                    </svg>
                    Trang chủ
                </a>
            </li>
            <li>
                <div class="flex items-center">
                    <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
                    </svg>
                    <span class="text-gray-400 ml-1 md:ml-2">Sản phẩm</span>
                </div>
            </li>
        </ol>
    </nav>

    <div class="flex flex-col lg:flex-row gap-8">
        <!-- Filters Sidebar -->
        <div class="w-full lg:w-64 flex-shrink-0">
            <div class="bg-white rounded-lg shadow p-6">
                <h2 class="text-lg font-medium text-gray-900 mb-4">Bộ lọc</h2>
                
                <!-- Categories -->
                <div class="mb-6">
                    <h3 class="text-sm font-medium text-gray-900 mb-2">Danh mục</h3>
                    <div class="space-y-2">
                        <a href="{% url 'products' %}" class="block text-sm text-gray-700 hover:text-blue-600 {% if not current_category %}font-medium text-blue-600{% endif %}">
                            Tất cả
                        </a>
                        {% for category in categories %}
                        <a href="?category={{ category.id }}{% if current_sort %}&sort={{ current_sort }}{% endif %}{% if current_price_range %}&price={{ current_price_range }}{% endif %}" 
                           class="block text-sm text-gray-700 hover:text-blue-600 {% if current_category == category.id|stringformat:'s' %}font-medium text-blue-600{% endif %}">
                            {{ category.name }}
                        </a>
                        {% endfor %}
                    </div>
                </div>

                <!-- Price Range -->
                <div class="mb-6">
                    <h3 class="text-sm font-medium text-gray-900 mb-2">Khoảng giá</h3>
                    <div class="space-y-2">
                        <a href="{% url 'products' %}{% if current_category %}?category={{ current_category }}{% endif %}{% if current_sort %}&sort={{ current_sort }}{% endif %}" 
                           class="block text-sm text-gray-700 hover:text-blue-600 {% if not current_price_range %}font-medium text-blue-600{% endif %}">
                            Tất cả
                        </a>
                        <a href="?price=0-5m{% if current_category %}&category={{ current_category }}{% endif %}{% if current_sort %}&sort={{ current_sort }}{% endif %}" 
                           class="block text-sm text-gray-700 hover:text-blue-600 {% if current_price_range == '0-5m' %}font-medium text-blue-600{% endif %}">
                            Dưới 5 triệu
                        </a>
                        <a href="?price=5m-10m{% if current_category %}&category={{ current_category }}{% endif %}{% if current_sort %}&sort={{ current_sort }}{% endif %}" 
                           class="block text-sm text-gray-700 hover:text-blue-600 {% if current_price_range == '5m-10m' %}font-medium text-blue-600{% endif %}">
                            5 - 10 triệu
                        </a>
                        <a href="?price=10m-20m{% if current_category %}&category={{ current_category }}{% endif %}{% if current_sort %}&sort={{ current_sort }}{% endif %}" 
                           class="block text-sm text-gray-700 hover:text-blue-600 {% if current_price_range == '10m-20m' %}font-medium text-blue-600{% endif %}">
                            10 - 20 triệu
                        </a>
                        <a href="?price=20m+{% if current_category %}&category={{ current_category }}{% endif %}{% if current_sort %}&sort={{ current_sort }}{% endif %}" 
                           class="block text-sm text-gray-700 hover:text-blue-600 {% if current_price_range == '20m+' %}font-medium text-blue-600{% endif %}">
                            Trên 20 triệu
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Products Grid -->
        <div class="flex-1">
            <!-- Sort and Search -->
            <div class="bg-white rounded-lg shadow p-4 mb-6">
                <div class="flex flex-col sm:flex-row justify-between items-center gap-4">
                    <!-- Sort -->
                    <div class="flex items-center space-x-2">
                        <span class="text-sm text-gray-700">Sắp xếp:</span>
                        <select onchange="window.location.href=this.value" class="text-sm border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                            <option value="{% url 'products' %}{% if current_category %}?category={{ current_category }}{% endif %}{% if current_price_range %}&price={{ current_price_range }}{% endif %}" 
                                    {% if current_sort == 'newest' or not current_sort %}selected{% endif %}>
                                Mới nhất
                            </option>
                            <option value="?sort=price_asc{% if current_category %}&category={{ current_category }}{% endif %}{% if current_price_range %}&price={{ current_price_range }}{% endif %}"
                                    {% if current_sort == 'price_asc' %}selected{% endif %}>
                                Giá tăng dần
                            </option>
                            <option value="?sort=price_desc{% if current_category %}&category={{ current_category }}{% endif %}{% if current_price_range %}&price={{ current_price_range }}{% endif %}"
                                    {% if current_sort == 'price_desc' %}selected{% endif %}>
                                Giá giảm dần
                            </option>
                            <option value="?sort=name{% if current_category %}&category={{ current_category }}{% endif %}{% if current_price_range %}&price={{ current_price_range }}{% endif %}"
                                    {% if current_sort == 'name' %}selected{% endif %}>
                                Tên A-Z
                            </option>
                        </select>
                    </div>

                    <!-- Search -->
                    <form method="GET" class="w-full sm:w-auto">
                        <div class="relative">
                            <input type="text" name="search" value="{{ search_query }}" placeholder="Tìm kiếm sản phẩm..." 
                                   class="w-full sm:w-64 pl-10 pr-4 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <svg class="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
                                </svg>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Products Grid -->
            {% if products %}
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
                {% for product in products %}
                <div class="group bg-white rounded-xl shadow-sm hover:shadow-xl transition-all duration-300 overflow-hidden flex flex-col h-[500px]">
                    <div class="relative h-64 flex-shrink-0">
                        <!-- Product Image -->
                        <a href="{% url 'product_detail' product.id %}" class="block overflow-hidden h-full">
                            {% if product.images.exists %}
                            <img src="{{ product.images.first.image.url }}" alt="{{ product.name }}" 
                                 class="w-full h-full object-cover transform group-hover:scale-105 transition-transform duration-300">
                            {% else %}
                            <div class="w-full h-full bg-gray-100 flex items-center justify-center">
                                <svg class="h-16 w-16 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                </svg>
                            </div>
                            {% endif %}
                        </a>
                        <!-- Category Badge -->
                        {% if product.category %}
                        <span class="absolute top-4 left-4 px-3 py-1.5 bg-blue-100 text-blue-800 text-xs font-medium rounded-full">
                            {{ product.category.name }}
                        </span>
                        {% endif %}
                        <!-- Stock Badge -->
                        {% if product.stock <= 5 and product.stock > 0 %}
                        <span class="absolute top-4 right-4 px-3 py-1.5 bg-yellow-100 text-yellow-800 text-xs font-medium rounded-full">
                            Còn {{ product.stock }} sản phẩm
                        </span>
                        {% endif %}
                    </div>

                    <div class="p-6 flex flex-col flex-grow">
                        <!-- Product Info -->
                        <div class="flex-grow">
                            <a href="{% url 'product_detail' product.id %}" class="block group-hover:text-blue-600 transition-colors duration-200">
                                <h3 class="text-lg font-semibold text-gray-900 leading-tight h-14 line-clamp-2">{{ product.name }}</h3>
                            </a>
                            <p class="text-sm text-gray-600 mt-2 h-10 line-clamp-2">{{ product.description }}</p>
                        </div>

                        <!-- Price & Action -->
                        <div class="flex items-baseline space-x-2 min-w-[120px]">
                            {% if product.original_price and product.original_price > product.price %}
                            <p class="text-sm text-gray-500 line-through">{{ product.original_price|format_price }}</p>
                            {% endif %}
                            <p class="text-lg font-bold text-blue-600">{{ product.price|format_price }}</p>
                        </div>
                     
                    </div>
                </div>
                {% endfor %}
            </div>

            <!-- Pagination -->
            {% if products.has_other_pages %}
            <div class="mt-8 flex justify-center">
                <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                    {% if products.has_previous %}
                    <a href="?page={{ products.previous_page_number }}{% if current_category %}&category={{ current_category }}{% endif %}{% if current_sort %}&sort={{ current_sort }}{% endif %}{% if current_price_range %}&price={{ current_price_range }}{% endif %}{% if search_query %}&search={{ search_query }}{% endif %}" 
                       class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                        <span class="sr-only">Previous</span>
                        <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                        </svg>
                    </a>
                    {% endif %}

                    {% for i in products.paginator.page_range %}
                        {% if products.number == i %}
                        <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-blue-50 text-sm font-medium text-blue-600">
                            {{ i }}
                        </span>
                        {% else %}
                        <a href="?page={{ i }}{% if current_category %}&category={{ current_category }}{% endif %}{% if current_sort %}&sort={{ current_sort }}{% endif %}{% if current_price_range %}&price={{ current_price_range }}{% endif %}{% if search_query %}&search={{ search_query }}{% endif %}" 
                           class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                            {{ i }}
                        </a>
                        {% endif %}
                    {% endfor %}

                    {% if products.has_next %}
                    <a href="?page={{ products.next_page_number }}{% if current_category %}&category={{ current_category }}{% endif %}{% if current_sort %}&sort={{ current_sort }}{% endif %}{% if current_price_range %}&price={{ current_price_range }}{% endif %}{% if search_query %}&search={{ search_query }}{% endif %}" 
                       class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                        <span class="sr-only">Next</span>
                        <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10l-3.293-3.293a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                        </svg>
                    </a>
                    {% endif %}
                </nav>
            </div>
            {% endif %}
            {% else %}
            <div class="text-center py-12">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">Không tìm thấy sản phẩm</h3>
                <p class="mt-1 text-sm text-gray-500">Không có sản phẩm nào phù hợp với tiêu chí tìm kiếm của bạn.</p>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Toast Messages -->
<div id="toast" class="fixed bottom-4 right-4 transform transition-transform duration-300 translate-y-full">
    <div class="bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg flex items-center">
        <svg class="h-6 w-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
        </svg>
        <span id="toastMessage"></span>
    </div>
</div>

{% endblock %}

{% block scripts %}
<script>
    function showToast(message) {
        const toast = document.getElementById('toast');
        const toastMessage = document.getElementById('toastMessage');
        toastMessage.textContent = message;
        toast.classList.remove('translate-y-full');
        setTimeout(() => {
            toast.classList.add('translate-y-full');
        }, 3000);
    }

    // Handle add to cart forms - only run once
    document.querySelectorAll('form[action*="add-to-cart"], form[action*="add_to_cart"]').forEach(form => {
        // Check if event listener already attached
        if (form.dataset.listenerAttached) return;
        form.dataset.listenerAttached = 'true';
        
        form.addEventListener('submit', async (e) => {
            e.preventDefault();
            
            // Prevent multiple submissions
            const submitBtn = form.querySelector('button[type="submit"]');
            if (submitBtn.disabled) return;
            submitBtn.disabled = true;
            
            console.log('Adding to cart:', form.action);
            
            try {
                const response = await fetch(form.action, {
                    method: 'POST',
                    body: new FormData(form),
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                });
                const data = await response.json();
                if (data.status === 'success') {
                    showToast(data.message);
                    // Update cart count
                    const cartCount = document.querySelector('.cart-count');
                    if (cartCount) {
                        cartCount.textContent = data.cart_count;
                    }
                } else {
                    showToast(data.message);
                }
            } catch (error) {
                console.error('Add to cart error:', error);
                showToast('Có lỗi xảy ra. Vui lòng thử lại.');
            } finally {
                // Re-enable button after a short delay
                setTimeout(() => {
                    submitBtn.disabled = false;
                }, 1000);
            }
        });
    });
</script>
{% endblock %} 