# DJANGO-ALLAUTH.
# Copyright (C) 2016
# This file is distributed under the same license as the django-allauth package.
#
# Translators:
# <PERSON> <steve.koss<PERSON><PERSON>@yahoo.fr>, 2016.
# <PERSON><PERSON> <<EMAIL>>, 2019
#
msgid ""
msgstr ""
"Project-Id-Version: django-allauth\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-01-23 12:50-0600\n"
"PO-Revision-Date: 2023-12-12 16:54+0100\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: français <>\n"
"Language: fr\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"
"X-Generator: Poedit *******\n"

#: account/adapter.py:50
msgid "Username can not be used. Please use other username."
msgstr "Ce pseudonyme ne peut pas être utilisé. Veuillez en choisir un autre."

#: account/adapter.py:56
msgid "Too many failed login attempts. Try again later."
msgstr ""
"Trop de tentatives de connexion échouées. Veuillez réessayer ultérieurement."

#: account/adapter.py:58
msgid "A user is already registered with this email address."
msgstr "Un autre utilisateur utilise déjà cette adresse e-mail."

#: account/adapter.py:59
msgid "Please type your current password."
msgstr "Merci d'indiquer votre mot de passe actuel."

#: account/adapter.py:60
msgid "Incorrect password."
msgstr "Mot de passe incorrect."

#: account/adapter.py:61
#, python-brace-format
msgid "Password must be a minimum of {0} characters."
msgstr "Le mot de passe doit contenir au minimum {0} caractères."

#: account/adapter.py:62
msgid "The email address is not assigned to any user account"
msgstr "Cette adresse email n'est pas associée à un compte utilisateur"

#: account/adapter.py:739
msgid "Use your password"
msgstr "Utilisez votre mot de passe"

#: account/adapter.py:749
msgid "Use your authenticator app"
msgstr "Utilisez votre application d'authentification"

#: account/admin.py:23
msgid "Mark selected email addresses as verified"
msgstr "Marquer les adresses e-mail sélectionnées comme vérifiées"

#: account/apps.py:11
msgid "Accounts"
msgstr "Comptes"

#: account/forms.py:60 account/forms.py:449
msgid "You must type the same password each time."
msgstr "Vous devez saisir deux fois le même mot de passe."

#: account/forms.py:92 account/forms.py:412 account/forms.py:554
#: account/forms.py:683
msgid "Password"
msgstr "Mot de passe"

#: account/forms.py:93
msgid "Remember Me"
msgstr "Se souvenir de moi"

#: account/forms.py:97
msgid "This account is currently inactive."
msgstr "Ce compte est actuellement désactivé."

#: account/forms.py:99
msgid "The email address and/or password you specified are not correct."
msgstr "L’adresse email ou le mot de passe sont incorrects."

#: account/forms.py:102
msgid "The username and/or password you specified are not correct."
msgstr "Le pseudo ou le mot de passe sont incorrects."

#: account/forms.py:112 account/forms.py:287 account/forms.py:477
#: account/forms.py:574
msgid "Email address"
msgstr "Adresse email"

#: account/forms.py:116 account/forms.py:325 account/forms.py:474
#: account/forms.py:569
msgid "Email"
msgstr "Email"

#: account/forms.py:119 account/forms.py:122 account/forms.py:277
#: account/forms.py:280
msgid "Username"
msgstr "Pseudonyme"

#: account/forms.py:132
msgid "Username or email"
msgstr "Pseudonyme ou email"

#: account/forms.py:135
msgctxt "field label"
msgid "Login"
msgstr "Identifiant"

#: account/forms.py:146
msgid "Forgot your password?"
msgstr "Mot de passe oublié ?"

#: account/forms.py:316
msgid "Email (again)"
msgstr "Email (confirmation)"

#: account/forms.py:320
msgid "Email address confirmation"
msgstr "Confirmation d'adresse email"

#: account/forms.py:328
msgid "Email (optional)"
msgstr "Email (facultatif)"

#: account/forms.py:383
msgid "You must type the same email each time."
msgstr "Vous devez saisir deux fois le même email."

#: account/forms.py:418 account/forms.py:557
msgid "Password (again)"
msgstr "Mot de passe (confirmation)"

#: account/forms.py:489
msgid "This email address is already associated with this account."
msgstr "L'adresse email est déjà associée à votre compte."

#: account/forms.py:491
#, python-format
msgid "You cannot add more than %d email addresses."
msgstr "Vous ne pouvez pas ajouter plus de %d adresses e-mail."

#: account/forms.py:529
msgid "Current Password"
msgstr "Mot de passe actuel"

#: account/forms.py:532 account/forms.py:632
msgid "New Password"
msgstr "Nouveau mot de passe"

#: account/forms.py:535 account/forms.py:633
msgid "New Password (again)"
msgstr "Nouveau mot de passe (confirmation)"

#: account/forms.py:653
msgid "The password reset token was invalid."
msgstr "Le jeton de réinitialisation de mot de passe est invalide."

#: account/models.py:21
msgid "user"
msgstr "utilisateur"

#: account/models.py:26 account/models.py:34 account/models.py:138
msgid "email address"
msgstr "adresse email"

#: account/models.py:28
msgid "verified"
msgstr "vérifié"

#: account/models.py:29
msgid "primary"
msgstr "principale"

#: account/models.py:35
msgid "email addresses"
msgstr "adresses email"

#: account/models.py:141
msgid "created"
msgstr "créé"

#: account/models.py:142
msgid "sent"
msgstr "envoyé"

#: account/models.py:143 socialaccount/models.py:63
msgid "key"
msgstr "clé"

#: account/models.py:148
msgid "email confirmation"
msgstr "confirmation par email"

#: account/models.py:149
msgid "email confirmations"
msgstr "confirmations par email"

#: mfa/adapter.py:20
msgid ""
"You cannot activate two-factor authentication until you have verified your "
"email address."
msgstr ""
"Vous ne pouvez pas activer l'authentification à deux facteurs tant que vous "
"n'avez pas vérifié votre adresse e-mail."

#: mfa/adapter.py:23
msgid ""
"You cannot add an email address to an account protected by two-factor "
"authentication."
msgstr ""
"Vous ne pouvez pas ajouter une adresse e-mail à un compte protégé par "
"l'authentification à deux facteurs."

#: mfa/adapter.py:25
msgid "Incorrect code."
msgstr "Code incorrect."

#: mfa/adapter.py:27
msgid "You cannot deactivate two-factor authentication."
msgstr "Vous ne pouvez pas désactiver l'authentification à deux facteurs."

#: mfa/apps.py:9
msgid "MFA"
msgstr "MFA"

#: mfa/forms.py:15 mfa/forms.py:17 mfa/forms.py:52
msgid "Code"
msgstr "Code"

#: mfa/forms.py:50
msgid "Authenticator code"
msgstr "Code de l'authentificateur"

#: mfa/models.py:19
msgid "Recovery codes"
msgstr "Codes de récupération"

#: mfa/models.py:20
msgid "TOTP Authenticator"
msgstr "Authentificateur TOTP"

#: socialaccount/adapter.py:31
#, python-format
msgid ""
"An account already exists with this email address. Please sign in to that "
"account first, then connect your %s account."
msgstr ""
"Un compte existe déjà avec cette adresse email. Merci de vous connecter au "
"préalable avec ce compte, et ensuite connecter votre compte %s."

#: socialaccount/adapter.py:148
msgid "Your account has no password set up."
msgstr "Vous devez d'abord définir le mot de passe de votre compte."

#: socialaccount/adapter.py:155
msgid "Your account has no verified email address."
msgstr "Vous devez d'abord associer une adresse email à votre compte."

#: socialaccount/apps.py:9
msgid "Social Accounts"
msgstr "Comptes sociaux"

#: socialaccount/models.py:37 socialaccount/models.py:91
msgid "provider"
msgstr "fournisseur"

#: socialaccount/models.py:46
msgid "provider ID"
msgstr "ID du fournisseur"

#: socialaccount/models.py:50
msgid "name"
msgstr "nom"

#: socialaccount/models.py:52
msgid "client id"
msgstr "id client"

#: socialaccount/models.py:54
msgid "App ID, or consumer key"
msgstr "ID de l'app ou clé de l'utilisateur"

#: socialaccount/models.py:57
msgid "secret key"
msgstr "clé secrète"

#: socialaccount/models.py:60
msgid "API secret, client secret, or consumer secret"
msgstr "Secret de l'API, secret du client, ou secret de l'utilisateur"

#: socialaccount/models.py:63
msgid "Key"
msgstr "Clé"

#: socialaccount/models.py:75
msgid "social application"
msgstr "application sociale"

#: socialaccount/models.py:76
msgid "social applications"
msgstr "applications sociales"

#: socialaccount/models.py:111
msgid "uid"
msgstr "uid"

#: socialaccount/models.py:113
msgid "last login"
msgstr "dernière identification"

#: socialaccount/models.py:114
msgid "date joined"
msgstr "date d'inscription"

#: socialaccount/models.py:115
msgid "extra data"
msgstr "données supplémentaires"

#: socialaccount/models.py:119
msgid "social account"
msgstr "compte social"

#: socialaccount/models.py:120
msgid "social accounts"
msgstr "comptes sociaux"

#: socialaccount/models.py:154
msgid "token"
msgstr "jeton"

#: socialaccount/models.py:155
msgid "\"oauth_token\" (OAuth1) or access token (OAuth2)"
msgstr "\"oauth_token\" (OAuth1) ou jeton d'accès (OAuth2)"

#: socialaccount/models.py:159
msgid "token secret"
msgstr "jeton secret"

#: socialaccount/models.py:160
msgid "\"oauth_token_secret\" (OAuth1) or refresh token (OAuth2)"
msgstr "\"oauth_token_secret\" (OAuth1) ou jeton d'actualisation (OAuth2)"

#: socialaccount/models.py:163
msgid "expires at"
msgstr "expire le"

#: socialaccount/models.py:168
msgid "social application token"
msgstr "jeton de l'application sociale"

#: socialaccount/models.py:169
msgid "social application tokens"
msgstr "jetons de l'application sociale"

#: socialaccount/providers/douban/views.py:37
msgid "Invalid profile data"
msgstr "Données de profil incorrectes"

#: socialaccount/providers/oauth/client.py:85
#, python-format
msgid ""
"Invalid response while obtaining request token from \"%s\". Response was: %s."
msgstr ""
"Réponse invalide lors de l'obtention du jeton de requête de \"%s\". La "
"réponse était : %s."

#: socialaccount/providers/oauth/client.py:119
#: socialaccount/providers/pocket/client.py:86
#, python-format
msgid "Invalid response while obtaining access token from \"%s\"."
msgstr "Réponse invalide lors de l'obtention du jeton d'accès depuis \"%s\"."

#: socialaccount/providers/oauth/client.py:140
#, python-format
msgid "No request token saved for \"%s\"."
msgstr "Aucun jeton de requête sauvegardé pour \"%s\"."

#: socialaccount/providers/oauth/client.py:191
#, python-format
msgid "No access token saved for \"%s\"."
msgstr "Aucun jeton d'accès sauvegardé pour \"%s\"."

#: socialaccount/providers/oauth/client.py:212
#, python-format
msgid "No access to private resources at \"%s\"."
msgstr "Impossible d'accéder aux ressources privées de \"%s\"."

#: socialaccount/providers/pocket/client.py:41
#, python-format
msgid "Invalid response while obtaining request token from \"%s\"."
msgstr "Réponse invalide lors de l'obtention du jeton de requête de \"%s\"."

#: templates/account/account_inactive.html:5
#: templates/account/account_inactive.html:9
msgid "Account Inactive"
msgstr "Compte inactif"

#: templates/account/account_inactive.html:11
msgid "This account is inactive."
msgstr "Ce compte est inactif."

#: templates/account/base_reauthenticate.html:5
#: templates/account/base_reauthenticate.html:9
msgid "Confirm Access"
msgstr "Confirmer l'accès"

#: templates/account/base_reauthenticate.html:11
msgid "Please reauthenticate to safeguard your account."
msgstr "Veuillez vous réauthentifier pour protéger votre compte."

#: templates/account/base_reauthenticate.html:17
msgid "Alternative options"
msgstr "Options alternatives"

#: templates/account/email.html:4 templates/account/email.html:8
msgid "Email Addresses"
msgstr "Adresses email"

#: templates/account/email.html:11
msgid "The following email addresses are associated with your account:"
msgstr "Les adresses email suivantes sont associées à votre compte :"

#: templates/account/email.html:23
msgid "Verified"
msgstr "Vérifiée"

#: templates/account/email.html:27
msgid "Unverified"
msgstr "Non vérifiée"

#: templates/account/email.html:32
msgid "Primary"
msgstr "Principale"

#: templates/account/email.html:42
msgid "Make Primary"
msgstr "Rendre Principale"

#: templates/account/email.html:45 templates/account/email_change.html:37
msgid "Re-send Verification"
msgstr "Renvoyer le message de vérification"

#: templates/account/email.html:48 templates/socialaccount/connections.html:40
msgid "Remove"
msgstr "Retirer"

#: templates/account/email.html:57
msgid "Add Email Address"
msgstr "Ajouter une adresse email"

#: templates/account/email.html:70
msgid "Add Email"
msgstr "Ajouter un email"

#: templates/account/email.html:79
msgid "Do you really want to remove the selected email address?"
msgstr "Voulez-vous vraiment retirer cette adresse email ?"

#: templates/account/email/account_already_exists_message.txt:4
#, python-format
msgid ""
"You are receiving this email because you or someone else tried to signup for "
"an\n"
"account using email address:\n"
"\n"
"%(email)s\n"
"\n"
"However, an account using that email address already exists.  In case you "
"have\n"
"forgotten about this, please use the password forgotten procedure to "
"recover\n"
"your account:\n"
"\n"
"%(password_reset_url)s"
msgstr ""
"Vous recevez cet email car vous ou quelqu'un d'autre a demandé à créer\n"
"un compte en utilisant cette adresse email :\n"
"\n"
"%(email)s\n"
"\n"
"Cependant, un compte utilisant cette adresse existe déjà. Au cas où vous "
"auriez\n"
"oublié, merci d'utiliser la fonction de récupération de mot de passe pour\n"
"récupérer votre compte :\n"
"\n"
"%(password_reset_url)s"

#: templates/account/email/account_already_exists_subject.txt:3
msgid "Account Already Exists"
msgstr "Ce compte existe déjà"

#: templates/account/email/base_message.txt:1
#, python-format
msgid "Hello from %(site_name)s!"
msgstr "Bonjour, c'est %(site_name)s !"

#: templates/account/email/base_message.txt:5
#, python-format
msgid ""
"Thank you for using %(site_name)s!\n"
"%(site_domain)s"
msgstr ""
"Merci d'utiliser %(site_name)s !\n"
"%(site_domain)s"

#: templates/account/email/base_notification.txt:5
msgid ""
"You are receiving this mail because the following change was made to your "
"account:"
msgstr ""

#: templates/account/email/base_notification.txt:10
#, python-format
msgid ""
"If you do not recognize this change then please take proper security "
"precautions immediately. The change to your account originates from:\n"
"\n"
"- IP address: %(ip)s\n"
"- Browser: %(user_agent)s\n"
"- Date: %(timestamp)s"
msgstr ""

#: templates/account/email/email_changed_message.txt:4
#, python-format
msgid "Your email has been changed from %(from_email)s to %(to_email)s."
msgstr ""

#: templates/account/email/email_changed_subject.txt:3
#, fuzzy
#| msgid "Cancel Change"
msgid "Email Changed"
msgstr "Annuler la modification"

#: templates/account/email/email_confirm_message.txt:4
#, fuzzy
#| msgid "You have confirmed %(email)s."
msgid "Your email has been confirmed."
msgstr "Vous avez confirmé %(email)s."

#: templates/account/email/email_confirm_subject.txt:3
#, fuzzy
#| msgid "email confirmation"
msgid "Email Confirmation"
msgstr "confirmation par email"

#: templates/account/email/email_confirmation_message.txt:5
#, python-format
msgid ""
"You're receiving this email because user %(user_display)s has given your "
"email address to register an account on %(site_domain)s.\n"
"\n"
"To confirm this is correct, go to %(activate_url)s"
msgstr ""
"Vous recevez cet e-mail car l'utilisateur %(user_display)s a indiqué votre "
"adresse pour se connecter à son compte sur %(site_domain)s.\n"
"\n"
"Pour confirmer que vous en êtes bien le propriétaire, allez à "
"%(activate_url)s"

#: templates/account/email/email_confirmation_subject.txt:3
msgid "Please Confirm Your Email Address"
msgstr "Confirmez votre adresse email"

#: templates/account/email/email_deleted_message.txt:4
#, python-format
msgid "Email address %(deleted_email)s has been removed from your account."
msgstr ""

#: templates/account/email/email_deleted_subject.txt:3
#, fuzzy
#| msgid "Remove"
msgid "Email Removed"
msgstr "Retirer"

#: templates/account/email/password_changed_message.txt:4
#, fuzzy
#| msgid "Your password is now changed."
msgid "Your password has been changed."
msgstr "Votre mot de passe a été modifié."

#: templates/account/email/password_changed_subject.txt:3
#, fuzzy
#| msgid "Password (again)"
msgid "Password Changed"
msgstr "Mot de passe (confirmation)"

#: templates/account/email/password_reset_key_message.txt:4
msgid ""
"You're receiving this email because you or someone else has requested a "
"password reset for your user account.\n"
"It can be safely ignored if you did not request a password reset. Click the "
"link below to reset your password."
msgstr ""
"Vous recevez cet email car vous ou quelqu'un d'autre a demandé le mot de "
"passe pour votre compte utilisateur.\n"
"Vous pouvez simplement ignorer ce message si vous n'êtes pas à l'origine de "
"cette demande. Sinon, cliquez sur le lien ci-dessous pour réinitialiser "
"votre mot de passe."

#: templates/account/email/password_reset_key_message.txt:9
#, python-format
msgid "In case you forgot, your username is %(username)s."
msgstr ""
"Au cas où vous l'auriez oublié, votre nom d'utilisateur est %(username)s."

#: templates/account/email/password_reset_key_subject.txt:3
#: templates/account/email/unknown_account_subject.txt:3
msgid "Password Reset Email"
msgstr "E-mail de réinitialisation de mot de passe"

#: templates/account/email/password_reset_message.txt:4
#, fuzzy
#| msgid "Your password is now changed."
msgid "Your password has been reset."
msgstr "Votre mot de passe a été modifié."

#: templates/account/email/password_reset_subject.txt:3
#: templates/account/password_reset.html:4
#: templates/account/password_reset.html:8
#: templates/account/password_reset_done.html:6
#: templates/account/password_reset_done.html:10
msgid "Password Reset"
msgstr "Réinitialisation du mot de passe"

#: templates/account/email/password_set_message.txt:4
#, fuzzy
#| msgid "Your password is now changed."
msgid "Your password has been set."
msgstr "Votre mot de passe a été modifié."

#: templates/account/email/password_set_subject.txt:3
#, fuzzy
#| msgid "Password Reset"
msgid "Password Set"
msgstr "Réinitialisation du mot de passe"

#: templates/account/email/unknown_account_message.txt:4
#, python-format
msgid ""
"You are receiving this email because you or someone else has requested a\n"
"password for your user account. However, we do not have any record of a "
"user\n"
"with email %(email)s in our database.\n"
"\n"
"This mail can be safely ignored if you did not request a password reset.\n"
"\n"
"If it was you, you can sign up for an account using the link below."
msgstr ""
"\"Vous recevez cet e-mail parce que vous ou quelqu'un d'autre a demandé un "
"mot de\n"
"passe pour votre compte utilisateur. Cependant, nous n'avons aucun\n"
"enregistrement d'un utilisateur avec l'e-mail %(email)s dans notre base de\n"
"données.\n"
"\n"
"Vous pouvez ignorer en toute sécurité cet e-mail si vous n'avez pas demandé "
"de\n"
"réinitialisation de mot de passe.\n"
"\n"
"Si c'était vous, vous pouvez vous inscrire pour un compte en utilisant le "
"lien\n"
"ci-dessous."

#: templates/account/email_change.html:5 templates/account/email_change.html:9
msgid "Email Address"
msgstr "Adresse email"

#: templates/account/email_change.html:21
#: templates/account/email_change.html:29
msgid "Current email"
msgstr "Email actuel"

#: templates/account/email_change.html:31
msgid "Changing to"
msgstr "Changer pour"

#: templates/account/email_change.html:35
msgid "Your email address is still pending verification."
msgstr "Votre adresse e-mail est toujours en attente de vérification."

#: templates/account/email_change.html:41
msgid "Cancel Change"
msgstr "Annuler la modification"

#: templates/account/email_change.html:49
msgid "Change to"
msgstr "Changer pour"

#: templates/account/email_change.html:55
#: templates/allauth/layouts/base.html:29
msgid "Change Email"
msgstr "Changer d'email"

#: templates/account/email_confirm.html:6
#: templates/account/email_confirm.html:10
msgid "Confirm Email Address"
msgstr "Confirmer l'adresse email"

#: templates/account/email_confirm.html:16
#, python-format
msgid ""
"Please confirm that <a href=\"mailto:%(email)s\">%(email)s</a> is an email "
"address for user %(user_display)s."
msgstr ""
"Merci de confirmer que <a href=\"mailto:%(email)s\">%(email)s</a> est "
"l'adresse email de %(user_display)s."

#: templates/account/email_confirm.html:23
#: templates/account/reauthenticate.html:20
#: templates/mfa/reauthenticate.html:20
msgid "Confirm"
msgstr "Confirmer"

#: templates/account/email_confirm.html:29
#: templates/account/messages/email_confirmation_failed.txt:2
#, python-format
msgid ""
"Unable to confirm %(email)s because it is already confirmed by a different "
"account."
msgstr ""
"Impossible de confirmer %(email)s car il est déjà confirmé par un autre "
"compte."

#: templates/account/email_confirm.html:35
#, python-format
msgid ""
"This email confirmation link expired or is invalid. Please <a "
"href=\"%(email_url)s\">issue a new email confirmation request</a>."
msgstr ""
"Ce lien de confirmation d'adresse e-mail a expiré ou n'est pas valide. "
"Veuillez lancer <a href=\"%(email_url)s\">une nouvelle demande de "
"confirmation</a>."

#: templates/account/login.html:5 templates/account/login.html:9
#: templates/account/login.html:29 templates/allauth/layouts/base.html:36
#: templates/mfa/authenticate.html:5 templates/mfa/authenticate.html:23
#: templates/openid/login.html:5 templates/openid/login.html:9
#: templates/openid/login.html:20 templates/socialaccount/login.html:5
msgid "Sign In"
msgstr "Connexion"

#: templates/account/login.html:12
#, python-format
msgid ""
"If you have not created an account yet, then please\n"
"    <a href=\"%(signup_url)s\">sign up</a> first."
msgstr ""
"Si vous n'avez pas encore créé de compte, merci de vous <a "
"href=\"%(signup_url)s\">enregistrer</a> au préalable."

#: templates/account/logout.html:4 templates/account/logout.html:8
#: templates/account/logout.html:23 templates/allauth/layouts/base.html:32
#: templates/usersessions/usersession_list.html:64
msgid "Sign Out"
msgstr "Se déconnecter"

#: templates/account/logout.html:10
msgid "Are you sure you want to sign out?"
msgstr "Etes-vous sûr(e) de vouloir vous déconnecter ?"

#: templates/account/messages/cannot_delete_primary_email.txt:2
#, python-format
msgid "You cannot remove your primary email address (%(email)s)."
msgstr ""
"Vous ne pouvez pas supprimer votre adresse email principale (%(email)s)."

#: templates/account/messages/email_confirmation_sent.txt:2
#, python-format
msgid "Confirmation email sent to %(email)s."
msgstr "E-mail de confirmation envoyé à %(email)s."

#: templates/account/messages/email_confirmed.txt:2
#, python-format
msgid "You have confirmed %(email)s."
msgstr "Vous avez confirmé %(email)s."

#: templates/account/messages/email_deleted.txt:2
#, python-format
msgid "Removed email address %(email)s."
msgstr "Adresse email %(email)s supprimée."

#: templates/account/messages/logged_in.txt:4
#, python-format
msgid "Successfully signed in as %(name)s."
msgstr "Connexion avec %(name)s réussie."

#: templates/account/messages/logged_out.txt:2
msgid "You have signed out."
msgstr "Vous êtes déconnecté."

#: templates/account/messages/password_changed.txt:2
msgid "Password successfully changed."
msgstr "Mot de passe modifié."

#: templates/account/messages/password_set.txt:2
msgid "Password successfully set."
msgstr "Mot de passe défini."

#: templates/account/messages/primary_email_set.txt:2
msgid "Primary email address set."
msgstr "Adresse email principale enregistrée."

#: templates/account/messages/unverified_primary_email.txt:2
msgid "Your primary email address must be verified."
msgstr "Votre adresse email principale doit être vérifiée."

#: templates/account/password_change.html:4
#: templates/account/password_change.html:8
#: templates/account/password_change.html:19
#: templates/account/password_reset_from_key.html:5
#: templates/account/password_reset_from_key.html:12
#: templates/account/password_reset_from_key.html:29
#: templates/account/password_reset_from_key_done.html:5
#: templates/account/password_reset_from_key_done.html:9
msgid "Change Password"
msgstr "Modifier le mot de passe"

#: templates/account/password_change.html:21
msgid "Forgot Password?"
msgstr "Mot de passe oublié ?"

#: templates/account/password_reset.html:14
msgid ""
"Forgotten your password? Enter your email address below, and we'll send you "
"an email allowing you to reset it."
msgstr ""
"Mot de passe oublié ? Indiquez votre adresse email ci-dessous et nous vous "
"enverrons un email pour le réinitialiser."

#: templates/account/password_reset.html:25
msgid "Reset My Password"
msgstr "Réinitialiser mon mot de passe"

#: templates/account/password_reset.html:29
msgid "Please contact us if you have any trouble resetting your password."
msgstr ""
"Merci de nous contacter si vous ne parvenez pas à réinitialiser votre mot de "
"passe."

#: templates/account/password_reset_done.html:16
msgid ""
"We have sent you an email. If you have not received it please check your "
"spam folder. Otherwise contact us if you do not receive it in a few minutes."
msgstr ""
"Nous vous avons envoyé un e-mail. Si vous ne l'avez pas reçu, veuillez "
"vérifier\n"
"votre dossier de spam. Sinon, contactez-nous si vous ne le recevez pas dans\n"
"quelques minutes."

#: templates/account/password_reset_from_key.html:10
msgid "Bad Token"
msgstr "Mauvais jeton d'identification"

#: templates/account/password_reset_from_key.html:18
#, python-format
msgid ""
"The password reset link was invalid, possibly because it has already been "
"used.  Please request a <a href=\"%(passwd_reset_url)s\">new password reset</"
"a>."
msgstr ""
"Le lien de réinitialisation du mot de passe est invalide. Il a peut être "
"déjà été utilisé. Veuillez faire une nouvelle <a "
"href=\"%(passwd_reset_url)s\">demande de réinitialisation de mot de passe</"
"a>."

#: templates/account/password_reset_from_key_done.html:11
msgid "Your password is now changed."
msgstr "Votre mot de passe a été modifié."

#: templates/account/password_set.html:5 templates/account/password_set.html:9
#: templates/account/password_set.html:20
msgid "Set Password"
msgstr "Définir un mot de passe"

#: templates/account/reauthenticate.html:5
msgid "Enter your password:"
msgstr "Entrez votre mot de passe:"

#: templates/account/signup.html:4 templates/socialaccount/signup.html:5
msgid "Signup"
msgstr "Inscription"

#: templates/account/signup.html:8 templates/account/signup.html:27
#: templates/allauth/layouts/base.html:39 templates/socialaccount/signup.html:9
#: templates/socialaccount/signup.html:29
msgid "Sign Up"
msgstr "Création de compte"

#: templates/account/signup.html:11
#, python-format
msgid ""
"Already have an account? Then please <a href=\"%(login_url)s\">sign in</a>."
msgstr ""
"Vous avez déjà un compte ? Vous pouvez donc <a href=\"%(login_url)s\">vous "
"connecter</a>."

#: templates/account/signup_closed.html:5
#: templates/account/signup_closed.html:9
msgid "Sign Up Closed"
msgstr "Inscriptions fermées"

#: templates/account/signup_closed.html:11
msgid "We are sorry, but the sign up is currently closed."
msgstr "Nous sommes désolés, mais les inscriptions sont actuellement fermées."

#: templates/account/snippets/already_logged_in.html:7
msgid "Note"
msgstr "Remarque"

#: templates/account/snippets/already_logged_in.html:7
#, python-format
msgid "You are already logged in as %(user_display)s."
msgstr "Vous êtes déjà connecté en tant que %(user_display)s."

#: templates/account/snippets/warn_no_email.html:3
msgid "Warning:"
msgstr "Attention :"

#: templates/account/snippets/warn_no_email.html:3
msgid ""
"You currently do not have any email address set up. You should really add an "
"email address so you can receive notifications, reset your password, etc."
msgstr ""
"Vous n'avez aucune adresse email associée à votre compte. Vous devriez "
"ajouter une adresse email pour pouvoir recevoir des notifications, "
"réinitialiser votre mot de passe, etc."

#: templates/account/verification_sent.html:5
#: templates/account/verification_sent.html:9
#: templates/account/verified_email_required.html:5
#: templates/account/verified_email_required.html:9
msgid "Verify Your Email Address"
msgstr "Vérifiez votre adresse email"

#: templates/account/verification_sent.html:12
msgid ""
"We have sent an email to you for verification. Follow the link provided to "
"finalize the signup process. If you do not see the verification email in "
"your main inbox, check your spam folder. Please contact us if you do not "
"receive the verification email within a few minutes."
msgstr ""
"Nous vous avons envoyé un email pour validation. Cliquez sur le lien fourni "
"dans l'email pour terminer l'inscription. Merci de nous contacter si vous ne "
"le recevez pas d'ici quelques minutes."

#: templates/account/verified_email_required.html:13
msgid ""
"This part of the site requires us to verify that\n"
"you are who you claim to be. For this purpose, we require that you\n"
"verify ownership of your email address. "
msgstr ""
"Pour accéder à cette partie du site, il faut d'abord que nous ayons vérifié "
"que vous êtes bien le propriétaire de l'adresse e-mail que vous nous avez "
"indiquée."

#: templates/account/verified_email_required.html:18
msgid ""
"We have sent an email to you for\n"
"verification. Please click on the link inside that email. If you do not see "
"the verification email in your main inbox, check your spam folder. "
"Otherwise\n"
"contact us if you do not receive it within a few minutes."
msgstr ""
"Nous vous avons envoyé un email de vérification. Merci de cliquer sur le "
"lien inclus dans ce courriel. Contactez-nous si vous ne l'avez pas reçu "
"d'ici quelques minutes."

#: templates/account/verified_email_required.html:23
#, python-format
msgid ""
"<strong>Note:</strong> you can still <a href=\"%(email_url)s\">change your "
"email address</a>."
msgstr ""
"<strong>Remarque :</strong> vous pouvez toujours <a "
"href=\"%(email_url)s\">changer votre adresse e-mail</a>."

#: templates/allauth/layouts/base.html:18
msgid "Messages:"
msgstr "Messages :"

#: templates/allauth/layouts/base.html:25
msgid "Menu:"
msgstr "Menu :"

#: templates/mfa/authenticate.html:9 templates/mfa/index.html:5
#: templates/mfa/index.html:9
msgid "Two-Factor Authentication"
msgstr "Authentification à deux facteurs"

#: templates/mfa/authenticate.html:12
msgid ""
"Your account is protected by two-factor authentication. Please enter an "
"authenticator code:"
msgstr ""
"Votre compte est protégé par l'authentification à deux facteurs. Veuillez "
"entrer un code d'authentification:"

#: templates/mfa/authenticate.html:27
msgid "Cancel"
msgstr "Annuler"

#: templates/mfa/email/recovery_codes_generated_message.txt:4
#, fuzzy
#| msgid "A new set of recovery codes has been generated."
msgid ""
"A new set of Two-Factor Authentication recovery codes has been generated."
msgstr "Un nouveau jeu de codes de récupération a été généré."

#: templates/mfa/email/recovery_codes_generated_subject.txt:3
#, fuzzy
#| msgid "Recovery Codes"
msgid "New Recovery Codes Generated"
msgstr "Codes de récupération"

#: templates/mfa/email/totp_activated_message.txt:4
#: templates/mfa/messages/totp_activated.txt:2
msgid "Authenticator app activated."
msgstr "Application d'authentification activée."

#: templates/mfa/email/totp_activated_subject.txt:3
#, fuzzy
#| msgid "Authenticator app activated."
msgid "Authenticator App Activated"
msgstr "Application d'authentification activée."

#: templates/mfa/email/totp_deactivated_message.txt:4
#: templates/mfa/messages/totp_deactivated.txt:2
msgid "Authenticator app deactivated."
msgstr "Application d'authentification désactivée."

#: templates/mfa/email/totp_deactivated_subject.txt:3
#, fuzzy
#| msgid "Authenticator app deactivated."
msgid "Authenticator App Deactivated"
msgstr "Application d'authentification désactivée."

#: templates/mfa/index.html:14 templates/mfa/totp/base.html:4
msgid "Authenticator App"
msgstr "Application d'authentification"

#: templates/mfa/index.html:18
msgid "Authentication using an authenticator app is active."
msgstr ""
"L'authentification à l'aide d'une application d'authentification est active."

#: templates/mfa/index.html:20
msgid "An authenticator app is not active."
msgstr "Une application d'authentification n'est pas active."

#: templates/mfa/index.html:28 templates/mfa/totp/deactivate_form.html:24
msgid "Deactivate"
msgstr "Désactiver"

#: templates/mfa/index.html:32 templates/mfa/totp/activate_form.html:32
msgid "Activate"
msgstr "Activer"

#: templates/mfa/index.html:42 templates/mfa/recovery_codes/base.html:4
#: templates/mfa/recovery_codes/generate.html:6
#: templates/mfa/recovery_codes/index.html:6
msgid "Recovery Codes"
msgstr "Codes de récupération"

#: templates/mfa/index.html:47 templates/mfa/recovery_codes/index.html:9
#, python-format
msgid ""
"There is %(unused_count)s out of %(total_count)s recovery codes available."
msgid_plural ""
"There are %(unused_count)s out of %(total_count)s recovery codes available."
msgstr[0] ""
"Il y a %(unused_count)s codes de récupération disponibles sur "
"%(total_count)s."
msgstr[1] ""
"Il y a %(unused_count)s codes de récupération disponibles sur "
"%(total_count)s."

#: templates/mfa/index.html:50
msgid "No recovery codes set up."
msgstr "Aucun code de récupération n'a été configuré."

#: templates/mfa/index.html:59
msgid "View"
msgstr "Afficher"

#: templates/mfa/index.html:65
msgid "Download"
msgstr "Télécharger"

#: templates/mfa/index.html:73 templates/mfa/recovery_codes/generate.html:29
msgid "Generate"
msgstr "Générer"

#: templates/mfa/messages/recovery_codes_generated.txt:2
msgid "A new set of recovery codes has been generated."
msgstr "Un nouveau jeu de codes de récupération a été généré."

#: templates/mfa/reauthenticate.html:5
msgid "Enter an authenticator code:"
msgstr "Entrez un code d'authentification:"

#: templates/mfa/recovery_codes/generate.html:9
msgid "You are about to generate a new set of recovery codes for your account."
msgstr ""
"Vous êtes sur le point de générer un nouveau jeu de codes de récupération "
"pour votre compte."

#: templates/mfa/recovery_codes/generate.html:11
msgid "This action will invalidate your existing codes."
msgstr "Cette action invalidera vos codes existants."

#: templates/mfa/recovery_codes/generate.html:13
msgid "Are you sure?"
msgstr "Êtes-vous sûr?"

#: templates/mfa/recovery_codes/index.html:13
msgid "Unused codes"
msgstr "Codes inutilisés"

#: templates/mfa/recovery_codes/index.html:25
msgid "Download codes"
msgstr "Télécharger les codes"

#: templates/mfa/recovery_codes/index.html:30
msgid "Generate new codes"
msgstr "Générer de nouveaux codes"

#: templates/mfa/totp/activate_form.html:4
#: templates/mfa/totp/activate_form.html:8
msgid "Activate Authenticator App"
msgstr "Activer l'application d'authentification"

#: templates/mfa/totp/activate_form.html:11
msgid ""
"To protect your account with two-factor authentication, scan the QR code "
"below with your authenticator app. Then, input the verification code "
"generated by the app below."
msgstr ""
"Pour protéger votre compte avec l'authentification à deux facteurs, scannez "
"le code QR ci-dessous avec votre application d'authentification. Ensuite, "
"saisissez le code de vérification généré par l'application ci-dessous."

#: templates/mfa/totp/activate_form.html:21
msgid "Authenticator secret"
msgstr "Secret d'authentification"

#: templates/mfa/totp/activate_form.html:24
msgid ""
"You can store this secret and use it to reinstall your authenticator app at "
"a later time."
msgstr ""
"Vous pouvez stocker ce secret et l'utiliser pour réinstaller votre "
"application d'authentification ultérieurement."

#: templates/mfa/totp/deactivate_form.html:5
#: templates/mfa/totp/deactivate_form.html:9
msgid "Deactivate Authenticator App"
msgstr "Désactiver l'application d'authentification"

#: templates/mfa/totp/deactivate_form.html:12
msgid ""
"You are about to deactivate authenticator app based authentication. Are you "
"sure?"
msgstr ""
"Vous êtes sur le point de désactiver l'authentification basée sur "
"l'application d'authentification. Êtes-vous sûr?"

#: templates/socialaccount/authentication_error.html:5
#: templates/socialaccount/authentication_error.html:9
#, fuzzy
#| msgid "Social Network Login Failure"
msgid "Third-Party Login Failure"
msgstr "Echec de la connexion au réseau social"

#: templates/socialaccount/authentication_error.html:11
#, fuzzy
#| msgid ""
#| "An error occurred while attempting to login via your social network "
#| "account."
msgid ""
"An error occurred while attempting to login via your third-party account."
msgstr ""
"Une erreur est survenue lors de la tentative de connexion à votre compte de "
"réseau social."

#: templates/socialaccount/connections.html:5
#: templates/socialaccount/connections.html:9
msgid "Account Connections"
msgstr "Comptes associés"

#: templates/socialaccount/connections.html:13
msgid ""
"You can sign in to your account using any of the following third-party "
"accounts:"
msgstr ""
"Vous pouvez vous connecter à votre compte en utilisant l'un des comptes "
"tiers suivants:"

#: templates/socialaccount/connections.html:45
#, fuzzy
#| msgid ""
#| "You currently have no social network accounts connected to this account."
msgid "You currently have no third-party accounts connected to this account."
msgstr "Aucun compte de réseau social n'est actuellement associé à ce compte."

#: templates/socialaccount/connections.html:48
msgid "Add a Third-Party Account"
msgstr "Ajouter un compte tiers"

#: templates/socialaccount/email/account_connected_message.txt:4
#, python-format
msgid ""
"A third-party account from %(provider)s has been connected to your account."
msgstr ""

#: templates/socialaccount/email/account_connected_subject.txt:3
#, fuzzy
#| msgid "Add a Third-Party Account"
msgid "Third-Party Account Connected"
msgstr "Ajouter un compte tiers"

#: templates/socialaccount/email/account_disconnected_message.txt:4
#, python-format
msgid ""
"A third-party account from %(provider)s has been disconnected from your "
"account."
msgstr ""

#: templates/socialaccount/email/account_disconnected_subject.txt:3
#, fuzzy
#| msgid "Add a Third-Party Account"
msgid "Third-Party Account Disconnected"
msgstr "Ajouter un compte tiers"

#: templates/socialaccount/login.html:10
#, python-format
msgid "Connect %(provider)s"
msgstr "Connecter %(provider)s"

#: templates/socialaccount/login.html:13
#, python-format
msgid "You are about to connect a new third-party account from %(provider)s."
msgstr ""
"Vous êtes sur le point de connecter un nouveau compte tiers de %(provider)s."

#: templates/socialaccount/login.html:17
#, python-format
msgid "Sign In Via %(provider)s"
msgstr "Connexion via %(provider)s"

#: templates/socialaccount/login.html:20
#, python-format
msgid "You are about to sign in using a third-party account from %(provider)s."
msgstr ""
"Vous êtes sur le point de vous connecter en utilisant un compte tiers de "
"%(provider)s."

#: templates/socialaccount/login.html:27
msgid "Continue"
msgstr "Continuer"

#: templates/socialaccount/login_cancelled.html:5
#: templates/socialaccount/login_cancelled.html:9
msgid "Login Cancelled"
msgstr "Ouverture de session annulée"

#: templates/socialaccount/login_cancelled.html:13
#, python-format
msgid ""
"You decided to cancel logging in to our site using one of your existing "
"accounts. If this was a mistake, please proceed to <a "
"href=\"%(login_url)s\">sign in</a>."
msgstr ""
"Vous avez annulé la connexion à notre site depuis l'un de vos comptes de "
"réseau social. S'il s'agit d'une erreur, merci de <a "
"href=\"%(login_url)s\">connecter</a>."

#: templates/socialaccount/messages/account_connected.txt:2
#, fuzzy
#| msgid "The social account has been connected."
msgid "The third-party account has been connected."
msgstr "Le compte social a bien été connecté."

#: templates/socialaccount/messages/account_connected_other.txt:2
#, fuzzy
#| msgid "The social account is already connected to a different account."
msgid "The third-party account is already connected to a different account."
msgstr "Ce compte social est déjà connecté à un autre compte."

#: templates/socialaccount/messages/account_disconnected.txt:2
#, fuzzy
#| msgid "The social account has been disconnected."
msgid "The third-party account has been disconnected."
msgstr "Le compte social a été déconnecté."

#: templates/socialaccount/signup.html:12
#, python-format
msgid ""
"You are about to use your %(provider_name)s account to login to\n"
"%(site_name)s. As a final step, please complete the following form:"
msgstr ""
"Vous êtes sur le point de vous connecter via votre compte %(provider_name)s "
"au site %(site_name)s. Merci de compléter le formulaire suivant pour "
"confirmer la connexion."

#: templates/socialaccount/snippets/login.html:9
msgid "Or use a third-party"
msgstr "Ou utilisez un service tiers"

#: templates/usersessions/messages/sessions_logged_out.txt:2
msgid "Signed out of all other sessions."
msgstr "Déconnecté de toutes les autres sessions."

#: templates/usersessions/usersession_list.html:6
#: templates/usersessions/usersession_list.html:10
msgid "Sessions"
msgstr "Sessions"

#: templates/usersessions/usersession_list.html:23
msgid "Started At"
msgstr "Commencé à"

#: templates/usersessions/usersession_list.html:24
msgid "IP Address"
msgstr "Adresse IP"

#: templates/usersessions/usersession_list.html:25
msgid "Browser"
msgstr "Navigateur"

#: templates/usersessions/usersession_list.html:27
msgid "Last seen at"
msgstr "Vu pour la dernière fois à"

#: templates/usersessions/usersession_list.html:47
msgid "Current"
msgstr "Actuel"

#: templates/usersessions/usersession_list.html:60
msgid "Sign Out Other Sessions"
msgstr "Déconnecter les autres sessions"

#: usersessions/apps.py:9
msgid "User Sessions"
msgstr "Sessions utilisateur"

#: usersessions/models.py:48
msgid "session key"
msgstr "Clé de session"

#~ msgid "The following email address is associated with your account:"
#~ msgstr "Les adresses email suivantes sont associées à votre compte :"

#~ msgid "Change Email Address"
#~ msgstr "Changer l'adresse e-mail"

#~ msgid ""
#~ "To safeguard the security of your account, please enter your password:"
#~ msgstr ""
#~ "Pour garantir la sécurité de votre compte, veuillez entrer votre mot de "
#~ "passe:"

#, python-format
#~ msgid ""
#~ "Please sign in with one\n"
#~ "of your existing third party accounts. Or, <a "
#~ "href=\"%(signup_url)s\">sign up</a>\n"
#~ "for a %(site_name)s account and sign in below:"
#~ msgstr ""
#~ "Merci d'ouvrir une session avec l'un de vos comptes sociaux. Vous pouvez "
#~ "aussi <a href=\"%(signup_url)s\">ouvrir un compte</a> %(site_name)s puis "
#~ "vous connecter ci-dessous :"

#~ msgid "or"
#~ msgstr "ou"

#~ msgid "change password"
#~ msgstr "modifier le mot de passe"

#~ msgid "OpenID Sign In"
#~ msgstr "Connexion OpenID"

#~ msgid "This email address is already associated with another account."
#~ msgstr "L'adresse e-mail est déjà associée à un autre compte."

#~ msgid ""
#~ "We have sent you an e-mail. Please contact us if you do not receive it "
#~ "within a few minutes."
#~ msgstr ""
#~ "Nous vous avons envoyé un e-mail. Merci de nous contacter si vous ne le "
#~ "recevez pas d'ici quelques minutes."

#~ msgid "Account"
#~ msgstr "Compte"

#~ msgid "The login and/or password you specified are not correct."
#~ msgstr "L'identifiant ou le mot de passe sont incorrects."

#~ msgid "Usernames can only contain letters, digits and @/./+/-/_."
#~ msgstr ""
#~ "Un pseudonyme ne peut contenir que des lettres, des chiffres, ainsi que "
#~ "@/./+/-/_."

#~ msgid "This username is already taken. Please choose another."
#~ msgstr "Ce pseudonyme est déjà utilisé, merci d'en choisir un autre."

#~ msgid "Shopify Sign In"
#~ msgstr "Connexion Shopify"

#~ msgid ""
#~ "You have confirmed that <a href=\"mailto:%(email)s\">%(email)s</a> is an "
#~ "e-mail address for user %(user_display)s."
#~ msgstr ""
#~ "Vous avez confirmé que l'adresse e-mail de l'utilsateur %(user_display)s "
#~ "est <a href=\"mailto:%(email)s\">%(email)s</a>."

#~ msgid "Thanks for using our site!"
#~ msgstr "Merci d'utiliser notre site !"
